{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/generate-sitemap.js && vite build", "preview": "vite preview", "deploy": "node scripts/generate-sitemap.js && vite build", "generate-sitemap": "node scripts/generate-sitemap.js"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.20", "@vis.gl/react-google-maps": "^1.5.0", "aws-amplify": "^5.0.15", "fontverter": "^2.0.0", "framer-motion": "^12.4.11", "html-to-image": "^1.11.11", "lottie-web": "^5.12.2", "lucide-react": "^0.451.0", "maplibre-gl": "^5.1.1", "motion": "^11.12.0", "opentype.js": "^1.3.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-audio-visualize": "^1.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-loading-skeleton": "^3.5.0", "react-map-gl": "^8.0.1", "react-markdown": "^10.1.0", "react-modal": "^3.16.1", "react-router-dom": "^6.14.2", "react-switch": "^7.0.0", "remark-gfm": "^4.0.1", "supercluster": "^8.0.1", "use-debounce": "^10.0.4", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.4.1", "esbuild": "^0.25.4", "vite": "^6.3.5"}}