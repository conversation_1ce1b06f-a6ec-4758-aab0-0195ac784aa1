# The Last Service Call
*How a broken elevator in 1973 changed the way we think about ownership*

*Published: May 28, 2025*

**Author: <PERSON><PERSON><PERSON>**

![author-image](/images/blog/carl-gustaf-ydstrom.jpeg)

![banner-image](/images/blog/the-last-service-call-2.jpeg)

![link-preview-image](/images/blog/the-last-service-call.jpg)

## The End of an Era

[dropcap]On March 14, 1973, at 3:47 PM, an Otis elevator technician named <PERSON> arrived at the Sears Tower in Chicago carrying a leather tool bag that weighed forty-two pounds. He'd driven ninety minutes through sleet to reach the building, only to discover that the broken elevator needed a part his bag didn't contain—a part that wouldn't arrive for three days. The elevator would remain out of service for a week. The repair bill came to $3,200, roughly $22,000 in today's dollars.

<PERSON> couldn't have known it, but he was living through the final days of an era. Within a decade, that leather bag would be obsolete. Within two decades, his entire profession would be unrecognizable. Today, when an Otis elevator malfunctions, the company often knows about it before the building does. The broken part is identified remotely, the nearest technician with the right component is dispatched, and the fix happens in hours, not days. The shift seems technological, but <PERSON>, the Harvard Business School professor who has spent decades studying competitive advantage, sees something more profound at work: "We used to wait until it broke and then we would send ssomebody to fix it," he told me recently. "Now, the whole concept of service is changing."

What <PERSON> describes isn't just an improvement in efficiency. It's a fundamental reimagining of what ownership means. For most of human history, when you bought something—a plow, a printing press, an elevator—you owned both its benefits and its burdens. Today, increasingly, you can have one without the other. This shift, from products to "products as a service," might seem like corporate jargon. But hidden within it is a paradox that challenges one of capitalism's core assumptions: The less you own, the more you might actually possess.

## The Burden of Ownership

[dropcap]Consider the modern office building. The property manager technically "owns" hundreds of assets: elevators, HVAC systems, fire suppression equipment, security cameras. But ownership, in the traditional sense, has become a fiction. They don't truly possess these things any more than a Netflix subscriber owns movies. What they have instead is something more valuable: outcomes without obstacles.

This transformation began, oddly enough, with jet engines. In the 1990s, Rolls-Royce faced a crisis. Airlines were buying fewer engines, and competition was brutal. The company's response seemed radical at the time: instead of selling engines, they would sell "power by the hour." Airlines would pay only for the hours an engine ran, while Rolls-Royce retained ownership and responsibility for maintenance.

The accountants were horrified. How could giving up ownership of your product be profitable? But something unexpected happened. Because Rolls-Royce now bore the cost of repairs, they had an unprecedented incentive to build engines that rarely broke. Because they could track every vibration and temperature fluctuation, they could spot problems before they became failures. The engines got better. The service got cheaper. Everyone won.

"Building the capabilities to do that is a non-trivial problem," Porter explained. "But once you have a product that you can actually track, that you can measure, that you can understand how it fails—you can take responsibility for it being up and running in a way that was never possible before."

## The Physical Renaissance

[dropcap]Here's where the story takes an unexpected turn. In an age of digital everything, when businesses are desperate to virtualize every possible process, the physical world is staging a comeback. But not in the way you might think.

Walk into a high-end restaurant in Copenhagen or Tokyo, and you might notice something peculiar: beautiful metallic tags on tables, walls, even wine bottles. They're not decorative. Scan one with your phone, and you might access the wine's story, leave a review, or call a server. The same objects appear in hotels, museums, even on industrial equipment. They represent a new category of object—neither fully physical nor purely digital, but something in between.

These hybrid objects solve a problem that pure digitization never could: the human need for tangible anchors in an abstract world. We are physical beings navigating physical spaces. No amount of apps or cloud computing changes that fundamental reality. The question isn't whether the physical world matters—it's how to make it speak the language of the digital age.

A startup called Thinkertags has taken this principle and turned it into something unexpectedly beautiful. Their golden tags—objects that wouldn't look out of place in a jewelry store—transform mundane business infrastructure into something approaching art. It's a reminder that in our rush to digitize everything, we forgot that humans care about aesthetics, about the pleasure of well-designed objects, about the statement that beautiful things make about the businesses that choose them.

## The Prediction Trap

[dropcap]But the real revolution isn't in making physical objects smart. It's in what happens when ownership becomes fluid. Porter calls this "a whole new revolution in how we service things," but that undersells the transformation. We're not just changing how we service things—we're eliminating the need for traditional service altogether.

The old model was built on information asymmetry. The repair company knew things the customer didn't. They profited from that knowledge gap, often at the customer's expense. (Morrison's forty-two-pound bag contained dozens of parts he'd probably never need, each marked up 300 percent.) The new model turns this on its head. When customers can see exactly how their assets perform, vendors can't hide behind complexity. They must create genuine value or perish.

This transparency has created something economists didn't predict: a race to the top. Companies like Carrier don't just sell air conditioners anymore; they guarantee specific temperatures. Michelin doesn't just sell tires; they sell miles traveled safely. The incentives align perfectly—the better the product performs, the more profitable the relationship.

## The Ownership Illusion

[dropcap]Yet for all this transformation, most businesses still operate under what I call the ownership illusion—the belief that possession equals control. They fill warehouses with spare parts "just in case." They hoard equipment past its useful life. They maintain armies of specialists for problems that rarely occur.

The numbers tell the story. The average piece of industrial equipment is utilized only 40 percent of the time. Office buildings sit empty 60 percent of their existence. Corporate IT infrastructure runs at 20 percent capacity. We've optimized for ownership, not outcomes.

The companies thriving in this new reality have made a counterintuitive discovery: giving up ownership often increases control. When you don't own the elevator, you don't worry about maintenance schedules. When you don't own the fleet vehicles, you don't manage repairs. When you don't own the IT infrastructure, you don't lose sleep over server failures. You focus on what matters—your actual business.

## The Beautiful Paradox

[dropcap]Which brings us back to Frank Morrison and his forty-two-pound bag. That bag represented everything wrong with the old model: inefficiency, uncertainty, information hoarding. But it also represented something we've lost: the craftsman's pride in tools, the physical weight of responsibility, the tangible nature of work.

The genius of the new model isn't that it eliminates the physical—it's that it makes the physical more meaningful. When every object can tell its story, when every asset can communicate its needs, when every surface can become an interface, the physical world becomes more valuable, not less.

Those golden tags appearing in forward-thinking businesses aren't just practical tools. They're a statement: We understand that the future isn't about choosing between atoms and bits. It's about making atoms behave more like bits—fluid, responsive, intelligent—while making bits feel more like atoms—beautiful, tangible, present.

Porter summarizes the transformation elegantly: "The whole notion of how you sell and what you sell is evolving." But even this understates the case. We're not just changing how we sell. We're reconsidering what it means to own, to service, to possess. We're discovering that in a connected world, the most powerful position isn't owning everything—it's orchestrating outcomes.

Morrison retired in 1998, twenty-five years after that service call in Chicago. By then, Otis had already begun its transformation. The leather bag had been replaced by handheld diagnostics. The three-day waits had become same-day service. But the real change was invisible: the very concept of a "service call" was disappearing. In its place was something Morrison's generation couldn't have imagined—a world where machines call for help before they break, where ownership is optional, where the physical and digital dance together.

We're all living through our own version of Morrison's moment. The question is: Will we cling to our forty-two-pound bags, or will we embrace the beautiful paradox of owning less to possess more?

***

*This article is part of our Operational Excellence series, exploring the principles that separate exceptional asset management from average approaches.*

## Related Articles

- [The Conundrums of Better Quality: Why We're Thinking About Asset Management All Wrong](https://thinkertags.com/research-and-insights/the-conundrums-of-better-quality)
- [3 Principles for Better Asset Management](https://thinkertags.com/research-and-insights/top-3-timeless-principles)
- [The Future of Asset Tracking with Thinkertags](https://thinkertags.com/research-and-insights/the-future-of-asset-tracking-with-thinkertags)