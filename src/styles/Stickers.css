.stickers-container {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.json-input {
  width: 100%;
  min-height: 200px;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-family: monospace;
  resize: vertical;
  background-color: #f8fafc;
  transition: border-color 0.2s;
}

.json-input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.parse-button,
.download-all-button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.parse-button {
  background-color: #4CAF50;
}

.download-all-button {
  background-color: #2196F3;
}

.parse-button:hover,
.download-all-button:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.parse-button:active,
.download-all-button:active {
  transform: translateY(0);
}

.download-all-button:disabled {
  background-color: #e2e8f0;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  color: #ef4444;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.stickers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
  width: 100%;
}

.sticker-wrapper {
  display: flex;
}

.sticker {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  background-color: white;
  transition: all 0.2s;
}

.sticker:hover {
  transform: translateY(-2px);
}

.sticker-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.download-button {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: white;
  background-color: #2196F3;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.download-button:hover {
  filter: brightness(1.1);
}

.download-button:disabled {
  background-color: #e2e8f0;
  cursor: not-allowed;
}