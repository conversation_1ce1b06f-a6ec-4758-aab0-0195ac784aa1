.font-converter-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 30px;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.font-converter-container h1 {
  margin-bottom: 30px;
  text-align: center;
  color: var(--primary-color);
}

.input-section {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.base64-input-container,
.filename-input-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.base64-input-container label,
.filename-input-container label {
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--primary-color);
}

.base64-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: var(--border-radius);
  font-family: monospace;
  font-size: 14px;
  resize: vertical;
  min-height: 120px;
}

.filename-input {
  width: 100%;
  max-width: 300px;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: var(--border-radius);
  font-size: 16px;
}

.error-message {
  margin: 15px 0;
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: var(--border-radius);
  text-align: center;
}

.loading-message {
  margin: 30px 0;
  padding: 20px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: var(--border-radius);
  text-align: center;
  font-size: 16px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.convert-button, .download-button {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.convert-button {
  background-color: var(--primary-color);
  color: white;
}

.convert-button:hover:not(:disabled) {
  background-color: #333;
}

.convert-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.download-button {
  background-color: #4caf50;
  color: white;
}

.download-button:hover {
  background-color: #388e3c;
}

/* Simplified styles */

.note {
  margin-top: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: var(--border-radius);
  font-size: 14px;
  color: #666;
}

.note p {
  margin: 0;
  margin-bottom: 10px;
}

.note ul {
  margin: 0 0 10px 20px;
  padding-left: 0;
}

.note li {
  margin-bottom: 5px;
}

.note p:last-child {
  margin-bottom: 0;
}
