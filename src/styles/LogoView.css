/* Hide navbar when logo view is active */
body.logo-view-page .logo-container,
body.logo-view-page .isometric-navbar {
  display: none !important;
}

/* Remove padding for the logo view page */
body.logo-view-page .app {
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: 100% !important;
}

.logo-view-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: white;
  position: relative;
  overflow: hidden;
}

.logo-text {
  font-family: 'The Bold Font', Arial Black, sans-serif;
  font-size: 72px;
  color: #121212;
  margin-bottom: 50px;
  white-space: nowrap;
  /* Add transition for smooth transformations */
  transition: transform 0.3s ease, letter-spacing 0.3s ease;
  /* Ensure 3D transforms work properly */
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* Individual letter styling for effects */
.logo-letter {
  display: inline-block;
  transition: transform 0.3s ease;
  transform-origin: center bottom;
}

/* Font status indicator */
.font-status {
  position: fixed;
  top: 20px;
  left: 20px;
  font-size: 12px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0.7;
}

/* Toggle controls button */
.toggle-controls-button {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  z-index: 101;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-controls-button:hover {
  background-color: #0055aa;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Controls styling - Compact horizontal layout */
.logo-controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(240, 240, 240, 0.98);
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 800px;
  z-index: 100;
  transition: opacity 0.3s, transform 0.3s;
}

/* Grid layout for controls */
.controls-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px 20px;
  margin-bottom: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 11px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.control-group input[type="range"] {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  background: #ddd;
  outline: none;
  border-radius: 2px;
  transition: background 0.2s;
}

.control-group input[type="range"]:hover {
  background: #ccc;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #0066cc;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.control-group input[type="range"]::-webkit-slider-thumb:hover {
  background: #0055aa;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.control-group input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #0066cc;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.control-group input[type="range"]::-moz-range-thumb:hover {
  background: #0055aa;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Control buttons */
.control-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 5px;
}

.control-button {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  letter-spacing: 0.3px;
}

.reset-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.reset-button:hover {
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(-1px);
}

.copy-button {
  background-color: #0066cc;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 102, 204, 0.2);
}

.copy-button:hover {
  background-color: #0055aa;
  box-shadow: 0 3px 6px rgba(0, 102, 204, 0.3);
  transform: translateY(-1px);
}

.export-button {
  background-color: #28a745;
  color: white;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.export-button:hover {
  background-color: #218838;
  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
  transform: translateY(-1px);
}

/* Active state for buttons */
.control-button:active {
  transform: translateY(0);
  box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .controls-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .logo-controls {
    width: 95%;
    padding: 12px 15px;
  }
  
  .controls-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px 15px;
  }
  
  .logo-text {
    font-size: 48px;
  }
}

@media (max-width: 480px) {
  .logo-controls {
    bottom: 10px;
    padding: 10px 12px;
  }
  
  .controls-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px 12px;
  }
  
  .control-group label {
    font-size: 10px;
  }
  
  .logo-text {
    font-size: 36px;
  }
  
  .control-button {
    padding: 6px 15px;
    font-size: 11px;
  }
}