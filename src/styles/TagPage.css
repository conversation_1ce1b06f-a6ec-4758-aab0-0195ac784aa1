.settings-container {
  padding-left: 4%;
  padding-right: 4%;
  height: 100vh;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px;
  /* overflow-y: auto; */
}

.settings-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.back-arrow {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #000000;
  font-size: 16px;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  outline: none;
}

.back-arrow:active {
  background: none;
}

.save-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-button:hover:not(:disabled) {
  background-color: #357ae8;
}

.save-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.5);
}

.save-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.settings-panel {
  flex-grow: 1;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
}

.settings-panel-body-title {
  font-size: 24px;
  font-weight: 700;
  margin: 15px 0px 10px 0px;
  text-align: left;
}

.settings-panel-body-title-two {
  font-size: 24px;
  font-weight: 700;
  margin: 15px 0px 10px 0px;
  text-align: left;
}

.settings-qr-code-group {
  display: flex;
  align-items: flex-start;
  border-radius: 10px;
  padding: 5px;
  margin-bottom: 30px;
  background-color: #f5f5f5;
  flex-direction: column;
}

.settings-input-group-original,
.settings-input-group {
  padding-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 15px;
  border-radius: 0 0 10px 10px;
  text-align: left;
  background: #F9F9F9;
}

.settings-input-group-two {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  text-align: left;
  background: #F9F9F9;
  /* border: 1px solid #E5E7EB; */
}

.toggle-icon {
  font-size: 40px !important;
  margin: 15px;
  color: #747;
}

.settings-search-result-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  min-height: 30px;
}

.settings-search-result-item:hover {
  background-color: #f5f5f5;
}

.settings-search-result-type {
  font-weight: bold;
  margin-right: 12px;
}

.settings-search-result-name {
  flex-grow: 1;
}

.settings-search-result-tag {
  background-color: #e0e0e0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.settings-footer-button {
  background-color: #3478F6;
  color: white;
  border: none;
  padding: 18px 24px;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin-bottom: 25px;
}

.settings-footer-button:hover:not(:disabled):not(.saving) {
  background-color: #357ae8;
}

.settings-footer-button:disabled,
.settings-footer-button.saving {
  background-color: #9E9E9E;
  cursor: default;
}

.settings-input-group-h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 6px;
}

.settings-input-group-h4-sub {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 500;
}

.settings-input-group-h3 {
  padding-left: 5px;
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: left;
}

.settings-input-group-h3-sub {
  padding-left: 5px;
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 6px;
}

.settings-input-group-h4-legal {
  color: #5f5f5f;
  font-size: 13px;
  font-weight: 400;
  padding-top: 5px;
  padding-bottom: 10px;
}

.toggle-div-collapsable-1-sub-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: none !important;
}

.toggle-text-container-parent {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
}

.link-primary-icon img {
  width: 16px;
  height: 16px;
  display: block;
}

.redirect-input-container {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 0;
  opacity: 0;
  will-change: max-height, opacity;
  position: relative;
}

.redirect-input-container.expanded {
  max-height: 200px;
  opacity: 1;
}

.location-collapse-container {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 0;
  opacity: 0;
  will-change: max-height, opacity;
  position: relative;
}

.location-collapse-container.expanded {
  max-height: 800px;
  opacity: 1;
}

/* Add animation for Connect to Asset section */
.asset-collapse-container {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 0;
  opacity: 0;
  will-change: max-height, opacity;
  position: relative;
}

.asset-collapse-container.expanded {
  max-height: 500px;
  opacity: 1;
}

.location-details-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  width: 100%;
  /* padding: 12px 0 0;
  margin-top: 10px; */
}

.location-info-column {
  flex: 1;
  min-width: 0;
}

.address-line {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.coords-line {
  font-size: 13px;
  font-weight: 400;
  color: #4B5563;
}

.update-location-button {
  flex-shrink: 0;
  width: auto;
  min-width: 100px;
  margin-left: auto;
  background-color: #3478F6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  letter-spacing: 0.01em;
  text-shadow: none;
}

.update-location-button:hover {
  background-color: #2c64c9;
}

.map-container {
  overflow: hidden;
  max-height: 400px;
  opacity: 1;
  margin: 0;
  transition: all 0.3s ease;
  margin-top: 0;
}

.selected-group-info {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.selected-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.selected-group-icon img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  display: block;
}

.selected-group-texts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.selected-group-name {
  font-weight: 600;
  font-size: 16px;
  color: #000000;
}

.selected-group-address {
  font-size: 14px;
  color: #666666;
}

.selected-group-clear {
  background: #EEEEEE;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-group-clear:hover {
  background: #DDDDDD;
}

.tags-list-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #828181;
}

.group-tag-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F5F5F5;
  border-radius: 6px;
  padding: 10px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.group-tag-item:hover {
  background-color: #ECECEC;
}

.group-tag-id {
  background: #E0E0E0;
  width: 80px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  color: #333333;
  margin-right: 8px;
  padding: 4px;
}

.group-tag-item .arrow {
  color: #000000;
  font-size: 18px;
}

.selected-group-header-info {
  flex: 1;
}

.selected-group-header-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.selected-group-header-category {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.remove-group-button {
  background: none;
  border: none;
  cursor: pointer;
  margin-left: 10px;
  padding: 4px;
}

.remove-group-img {
  width: 18px;
  height: 18px;
}

.divider-line {
  border-top: 1px solid #dedede;
  width: 100%;
  margin: 8px 0;
}

.tags-list-wrapper {
  background: #F9F9F9;
  border-radius: 1px;
  padding: 1px;
}

.settings-footer-button.delete-button {
  background-color: #d81b60;
}

.settings-footer-button.delete-button:hover:not(:disabled) {
  background-color: #c2185b;
}

.settings-footer-button.delete-button:disabled {
  background-color: #f48fb1;
  cursor: not-allowed;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  accent-color: #4285f4;
  cursor: pointer;
}