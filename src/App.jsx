/* Import External */
import { Auth, API, Hub } from "aws-amplify";
import React, { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, useNavigate, Navigate } from 'react-router-dom';

/* Import Components */
import { Navbar } from './components/Navbar';
import { useCookies } from 'react-cookie';
import UpdateSuccessful from './components/UpdateSuccess';
import TemplatesUploadSuccess from './components/TemplatesUploadSuccess';
import { Stickers } from './components/Stickers';
import { LoadingLogo } from './components/LoadingLogo';
import { LocationProvider } from './components/LocationContext';

/* Import Views */
import { AssetList } from './views/AssetList';
import { AssetPage } from './views/AssetPage';
import { AssetDocumentView } from './views/AssetDocumentView';
import { AssetWorkbench } from './views/AssetWorkbench';
import { InviteUserView } from './views/InviteUserView';
import Login from "./views/Login";
import Signup from "./views/Signup";
import { TagPage } from "./views/TagPage";
import { TagList } from "./views/TagList";
import { TagRedirect } from "./components/TagRedirect";
import { ClaimTag } from "./views/ClaimTag";
import { VerifyInvite } from './views/VerifyInvite';
import { TagPageClaim } from './views/TagPageClaim';
import { LandingPage } from './views/LandingPage';
import { UserPage } from './views/UserPage';
import { StoreFrontPage } from './views/StoreFrontPage';
import { ForgotPassword } from './views/ForgotPassword';
import { BlogPost } from './views/BlogPost';
import FontConverter from './views/FontConverter';
import { LogoView } from './views/LogoView';

/* Form Views */
import { ProductPage } from './views/ProductPage';
import { EAIntroduction } from './views/forms/ea/operations-scale/EAIntroduction';
import { AssetsPortfolio } from './views/forms/ea/operations-scale/AssetsPortfolio';
import { ServiceVolume } from './views/forms/ea/operations-scale/ServiceVolume';
import { GeographicDistribution } from './views/forms/ea/operations-scale/GeographicDistribution';
import { TeamComposition } from './views/forms/ea/operations-scale/TeamComposition';
import { CurrentDocumentation } from './views/forms/ea/operations-scale/CurrentDocumentation';
import { OperationalPriorities } from './views/forms/ea/operational-priorities/OperationalPriorities';
import { QualificationResults } from "./views/forms/ea/results/QualificationResults";
import { NextSteps } from "./views/forms/ea/results/NextSteps";
import { ContactInformationEmail } from "./views/forms/ea/results/ContactInformationEmail";
import { ContactInformationName } from "./views/forms/ea/results/ContactInformationName";
import { ConfirmationExperience } from "./views/forms/ea/results/ConfirmationExperience";


import './App.css';

const App = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [cookies, setCookie, removeCookie] = useCookies(['idToken']);

  useEffect(() => {
    const checkAndRedirect = () => {
      const currentDomain = window.location.hostname;
      const domainsToRedirect = [
        'thinkertags.de',
        'thinkertags.fr',
        'thinkertags.es',
        'thinkertag.com',
        'thinkertags.co.uk'
      ];

      if (domainsToRedirect.includes(currentDomain)) {
        const currentPath = window.location.pathname + window.location.search;
        window.location.href = `https://thinkertags.com${currentPath}`;
        return;
      }
    };

    checkAndRedirect();
  }, []);

  // Get the current logged in user info
  const getUser = async () => {
    try {
      const authenticatedUser = await Auth.currentAuthenticatedUser();
      const userInfo = await Auth.currentUserInfo();

      setUser(userInfo);
      const session = await Auth.currentSession();
      const idToken = session.getIdToken().getJwtToken();

      if (shouldRefreshToken(cookies.idToken)) {
        setCookie('idToken', idToken, {
          path: '/',
          domain: '.thinkertags.com',
          sameSite: 'none',
          secure: true,
          maxAge: 24 * 60 * 60,
        });
      }
    } catch (error) {
      setUser(null);
      removeCookie('idToken', {domain: '.thinkertags.com' });
    } finally {
      setLoading(false);
    }
  };

  // Logout the authenticated user
  const signOut = async () => {
    await Auth.signOut();
    // Remove the 'idToken' cookie
    removeCookie('idToken', {domain: '.thinkertags.com' });
    setUser(null);
  };

  const shouldRefreshToken = (idToken) => {
    if (!idToken) {
      // No token, should refresh
      return true;
    }

    try {
      const [, payload] = idToken.split('.');
      const decodedPayload = JSON.parse(window.atob(payload));
      const expTime = decodedPayload.exp;
      const currentTime = Math.floor(Date.now() / 1000); // Current time in Unix timestamp

      // Refresh if the token is set to expire in the next 5 minutes (300 seconds)
      return expTime - currentTime < 300;
    } catch (error) {
      // If there's an error in decoding the token, assume it needs refresh
      return true;
    }
  };

  useEffect(() => {
    getUser();
  }, []);

  // Protected route component
  const ProtectedRoute = ({ children }) => {
    return user ? children : <Navigate to="/login" />;
  };

  if (loading) return <LoadingLogo />;

  return (
    <LocationProvider>
    <Router
    future={{
      v7_startTransition: true,
      v7_relativeSplatPath: true
    }}>

      <div className="container">
        <div className="app">
        <Navbar user={user} setUser={setUser} />
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={user ? <Navigate to="/tags" /> : <Login setUser={setUser} />} />
          <Route path="/signup" element={user ? <Navigate to="/tags" /> : <Signup />} />
          <Route path="/claim/:tagId" element={<ClaimTag />} />
          <Route path="/claim/:tagId/setup" element={<TagPageClaim />} />
          <Route path="/invitation/:username" element={<VerifyInvite />} />
          <Route path="/go" element={<TagRedirect />} />
          <Route path="/assets/:assetId/storefront/view" element={<ProductPage />} />
          <Route path="/assets/:assetId/documents/:documentId" element={<AssetDocumentView />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/research-and-insights/:postId" element={<BlogPost />} />
          <Route path="/woff-downloader" element={<FontConverter />} />
          <Route path="/logo-view" element={<LogoView />} />

          {/* Public routes - Guides */}
          <Route path="/f/ea/operations-scale/introduction" element={<EAIntroduction />} />
          <Route path="/f/ea/operations-scale/assets-portfolio" element={<AssetsPortfolio />} />
          <Route path="/f/ea/operations-scale/service-volume" element={<ServiceVolume />} />
          <Route path="/f/ea/operations-scale/geographic-distribution" element={<GeographicDistribution />} />
          <Route path="/f/ea/operations-scale/team-composition" element={<TeamComposition />} />
          <Route path="/f/ea/operations-scale/current-documentation" element={<CurrentDocumentation />} />
          <Route path="/f/ea/operational-priorities" element={<OperationalPriorities />} />
          <Route path="/f/ea/qualification-results" element={<QualificationResults />} />
          <Route path="/f/ea/next-steps" element={<NextSteps />} />
          <Route path="/f/ea/contact-information-name" element={<ContactInformationName />} />
          <Route path="/f/ea/contact-information-email" element={<ContactInformationEmail />} />
          <Route path="/f/ea/confirmation" element={<ConfirmationExperience />} />

          {/* Protected routes */}
          <Route path="/tags" element={<ProtectedRoute><TagList /></ProtectedRoute>} />
          <Route path="/tags/:tagId" element={<ProtectedRoute><TagPage /></ProtectedRoute>} />
          <Route path="/tags/:tagId/success" element={<ProtectedRoute><UpdateSuccessful /></ProtectedRoute>} />
          <Route path="/assets/:assetId" element={<ProtectedRoute><AssetPage /></ProtectedRoute>} />
          <Route path="/assets/:assetId/storefront" element={<ProtectedRoute><StoreFrontPage /></ProtectedRoute>} />
          <Route path="/assets/workbench" element={<ProtectedRoute><AssetWorkbench /></ProtectedRoute>} />
          <Route path="/assets" element={<ProtectedRoute><AssetList /></ProtectedRoute>} />
          <Route path="/assets/upload/success" element={<ProtectedRoute><TemplatesUploadSuccess /></ProtectedRoute>} />
          <Route path="/invite" element={<ProtectedRoute><InviteUserView /></ProtectedRoute>} />
          <Route path="/stickers" element={<ProtectedRoute><Stickers /></ProtectedRoute>} />
          <Route path="/users/:username" element={<ProtectedRoute><UserPage /></ProtectedRoute>} />

          {/* Catch-all route - redirects unknown paths to home */}
          <Route path="*" element={<Navigate to="/" />} />

        </Routes>
      </div>
      </div>
    </Router>
    </LocationProvider>
  );
};

export default App ;