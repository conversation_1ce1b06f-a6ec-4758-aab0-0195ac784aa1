import React, { useState, useEffect } from 'react';
import Skeleton from 'react-loading-skeleton';
import { Input } from '../Input';
import { ServiceButton } from './ServiceButton';
import { ProductImage } from './ProductImage';
import 'react-loading-skeleton/dist/skeleton.css';
import '../../styles/ProductHeader.css';

export const Header = ({ 
  data, 
  isLoading, 
  isEditing, 
  onSerialChange, 
  onImageUpload, 
  onButtonConfigChange,
  onRequestButtonClick,
  onNameChange
}) => {
  const [openPanels, setOpenPanels] = useState({
    details: true,
    contactButton: isEditing
  });
  
  // State for service button hover effect
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  
  // Service button status
  const [serviceButtonStatus, setServiceButtonStatus] = useState({
    loading: false,
    error: null
  });

  // Helper function to check if a string has content
  const hasContent = (str) => {
    return str && typeof str === 'string' && str.trim().length > 0;
  };

  if (isLoading) {
    return (
      <div className="ph-settings-panel">
        <div className="ph-preview-panel">
          <div className="ph-header">
            <div className="ph-image-container">
              <div className="ph-image-skeleton">
                <Skeleton width="100%" height="100%" borderRadius={8} />
              </div>
            </div>
            <div className="ph-content-wrapper">
              <div className="ph-input-group">
                <label><Skeleton width={100} /></label>
                <Skeleton height={41} borderRadius={4} />
              </div>
              <div className="ph-input-group">
                <label><Skeleton width={80} /></label>
                <Skeleton height={41} borderRadius={4} />
              </div>
              <div className="ph-input-group">
                <label><Skeleton width={70} /></label>
                <Skeleton height={80} borderRadius={4} />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleButtonConfigChange = (field, value) => {
    if (typeof onButtonConfigChange === 'function') {
      onButtonConfigChange(field, value);
    } else {
      console.warn('onButtonConfigChange is not a function');
    }
  };

  // Handle the service request submission from the ServiceButton component
  const handleServiceRequest = async (formData) => {
    if (typeof onRequestButtonClick !== 'function') {
      console.warn('onRequestButtonClick is not a function');
      return;
    }

    setServiceButtonStatus({ loading: true, error: null });
    
    try {
      await onRequestButtonClick({
        email: data.buttonEmail,
        subject: data.buttonSubject || `Service Request for ${data.name}`,
        message: data.buttonMessage || `A service request has been submitted for ${data.name}.`,
        userName: formData.userName,
        userEmail: formData.userEmail,
        userMessage: formData.userMessage,
        productName: data.name,
        serialNumber: data.serialNumber
      });
      
      setServiceButtonStatus({ loading: false, error: null });
    } catch (error) {
      console.error('Error in service button handler:', error);
      setServiceButtonStatus({ 
        loading: false, 
        error: error.message || "Failed to send service request. Please try again."
      });
      throw error; // Re-throw to let the ServiceButton component handle it
    }
  };
  
  const togglePanel = (panel) => {
    setOpenPanels(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  };

  // SVG for arrow pointing down (to expand)
  const downArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 9.5L1 4.5L2 3.5L6 7.5L10 3.5L11 4.5L6 9.5Z" fill="currentColor"/>
    </svg>
  );

  // SVG for arrow pointing up (to collapse)
  const upArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 2.5L11 7.5L10 8.5L6 4.5L2 8.5L1 7.5L6 2.5Z" fill="currentColor"/>
    </svg>
  );

  // SVG for service icon
  const serviceIcon = (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19.5 10.5C19.5 15.75 12 20.25 12 20.25C12 20.25 4.5 15.75 4.5 10.5C4.5 8.51088 5.29018 6.60322 6.6967 5.1967C8.10322 3.79018 10.0109 3 12 3C13.9891 3 15.8968 3.79018 17.3033 5.1967C18.7098 6.60322 19.5 8.51088 19.5 10.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 12.75C13.2426 12.75 14.25 11.7426 14.25 10.5C14.25 9.25736 13.2426 8.25 12 8.25C10.7574 8.25 9.75 9.25736 9.75 10.5C9.75 11.7426 10.7574 12.75 12 12.75Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
  
  // SVG for mail icon
  const mailIcon = (
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 5.25L12 13.5L3 5.25" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3 5.25H21V18C21 18.1989 20.921 18.3897 20.7803 18.5303C20.6397 18.671 20.4489 18.75 20.25 18.75H3.75C3.55109 18.75 3.36032 18.671 3.21967 18.5303C3.07902 18.3897 3 18.1989 3 18V5.25Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.3636 12L3.23126 18.538" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M20.7688 18.538L13.6363 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // Check if email is valid for display purposes
  const isValidEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  // Check if the service button is properly configured
  const isServiceButtonConfigured = Boolean(
    data.buttonText && 
    isValidEmail(data.buttonEmail)
  );

  return (
    <div className="ph-settings-panel">
      {/* Product Image and Basic Information */}
      <div className="ph-panel-section ph-header-container">
        <div className="ph-header">
          <div className="ph-image-container">
            <ProductImage
              imageUrl={data.imageUrl}
              name={data.name}
              isEditing={isEditing}
              onImageUpload={onImageUpload}
              imageEnabled={data.imageEnabled !== false}
            />
          </div>

          <div className="ph-info-container">
            {/* Product Details Panel */}
            <div 
              className={`toggle-div-collapsable-1-sub-1 ${openPanels.details ? 'expanded' : ''}`}
              onClick={() => togglePanel('details')}
            >
              <div className="toggle-text-container-parent">
                <div className="link-primary-icon">
                  <img
                    src="/basic_information.svg"
                    alt="Product Details"
                    width="16"
                    height="16"
                    onError={(e) => {e.target.style.display = 'none'}}
                  />
                </div>
                <div className="toggle-text-container">
                  <div className="settings-input-group-h4">Details</div>
                  <div className="settings-input-group-h4-sub">
                    Essential information
                  </div>
                </div>
              </div>
              <div className="collapse-icon-container">
                <div className="collapse-icon">
                  {openPanels.details ? upArrowSvg : downArrowSvg}
                </div>
              </div>
            </div>

            {/* Use location-collapse-container for smooth animation */}
            <div className={`location-collapse-container ${openPanels.details ? 'expanded' : ''}`}>
              <div
                style={{
                  borderTop: '1px solid #E5E7EB',
                  margin: '12px 0',
                }}
              />
              
              <div className="ph-section-content">
                <div className="ph-input-group">
                  <label htmlFor="product-name">Name</label>
                  {isEditing ? (
                    <Input
                      className="input-v2"
                      name="name"
                      id="product-name"
                      placeholder="Enter product name"
                      value={data.name || ''}
                      onChange={(e) => onNameChange(e.target.value)}
                    />
                  ) : (
                    <Input
                      className="input-v2 ph-readonly-input"
                      name="name"
                      id="product-name"
                      value={data.name || ''}
                      readOnly
                      placeholder="Untitled Product"
                    />
                  )}
                </div>

                {/* Only render Serial Number field if editing or if it has content */}
                {(isEditing || hasContent(data.serialNumber)) && (
                  <div className="ph-input-group">
                    <label htmlFor="serial-number">Serial Number</label>
                    {isEditing ? (
                      <Input
                        className="input-v2"
                        name="serialNumber"
                        id="serial-number"
                        placeholder="Enter serial number"
                        value={data.serialNumber || ''}
                        onChange={(e) => onSerialChange(e.target.value)}
                      />
                    ) : (
                      <Input
                        className="input-v2 ph-readonly-input"
                        name="serialNumber"
                        id="serial-number"
                        value={data.serialNumber || ''}
                        readOnly
                        placeholder="No serial number specified"
                      />
                    )}
                  </div>
                )}

                {/* Only render Description field if editing or if it has content */}
                {(isEditing || hasContent(data.description)) && (
                  <div className="ph-input-group">
                    <label htmlFor="product-description">Description</label>
                    <textarea
                      id="product-description"
                      className={`input-v2 ${!isEditing ? 'ph-readonly-textarea' : ''}`}
                      value={data.description || ''}
                      onChange={isEditing ? (e) => handleButtonConfigChange('description', e.target.value) : undefined}
                      readOnly={!isEditing}
                      rows={3}
                      placeholder="No description provided"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Button Section */}
      {(isEditing || data.buttonEnabled !== false) && (
        <div className="ph-panel-section ph-service-section">
          <div 
            className={`toggle-div-collapsable-1-sub-1 ${openPanels.contactButton ? 'expanded' : ''}`}
            onClick={() => isEditing ? togglePanel('contactButton') : null}
            style={{ cursor: isEditing ? 'pointer' : 'default' }}
          >
            <div className="toggle-text-container-parent">
              <div className="link-primary-icon">
                <img
                  src="/service_button.svg"
                  alt="Service Button"
                  width="16"
                  height="16"
                  onError={(e) => {e.target.style.display = 'none'}}
                />
              </div>
              <div className="toggle-text-container">
                <div className="settings-input-group-h4">Contact</div>
                <div className="settings-input-group-h4-sub">
                  Contact options
                </div>
              </div>
            </div>
            {isEditing && (
              <div className="collapse-icon-container">
                <div className="collapse-icon">
                  {openPanels.contactButton ? upArrowSvg : downArrowSvg}
                </div>
              </div>
            )}
          </div>

          {/* Use location-collapse-container for smooth animation */}
          <div className={`location-collapse-container ${openPanels.contactButton || !isEditing ? 'expanded' : ''}`}>
            <div
              style={{
                borderTop: '1px solid #E5E7EB',
                margin: '12px 0',
              }}
            />
            
            {/* Edit mode content */}
            {isEditing ? (
              <div className="ph-section-content" style={{display: openPanels.contactButton ? 'block' : 'none'}}>
                <div className="ph-service-form">
                  <div className="ph-form-left">
                    <div className="ph-input-group">
                      <label htmlFor="button-text">
                        Button Text
                        <span className="ph-required">*</span>
                      </label>
                      <Input
                        id="button-text"
                        className="input-v2"
                        value={data.buttonText || 'Request Service'}
                        onChange={(e) => handleButtonConfigChange('buttonText', e.target.value)}
                        placeholder="e.g., Request Service"
                      />
                    </div>
                    
                    <div className="ph-input-group">
                      <label htmlFor="button-email">
                        Recipient Email
                        <span className="ph-required">*</span>
                      </label>
                      <div className="ph-input-with-icon">
                        <div className="ph-input-icon">
                          {mailIcon}
                        </div>
                        <Input
                          id="button-email"
                          className="input-v2"
                          type="email"
                          isEmail
                          value={data.buttonEmail || ''}
                          onChange={(e) => handleButtonConfigChange('buttonEmail', e.target.value)}
                          placeholder="e.g., <EMAIL>"
                        />
                      </div>
                      {data.buttonEmail && !isValidEmail(data.buttonEmail) && (
                        <div className="ph-error-message">Please enter a valid email address</div>
                      )}
                    </div>
                    
                    <div className="ph-input-group">
                      <label htmlFor="button-subject">Email Subject</label>
                      <Input
                        id="button-subject"
                        className="input-v2"
                        value={data.buttonSubject || `Service Request for ${data.name}`}
                        onChange={(e) => handleButtonConfigChange('buttonSubject', e.target.value)}
                        placeholder="e.g., Service Request"
                      />
                    </div>
                  </div>
                  
                  <div className="ph-form-right">
                    <div className="ph-input-group ph-message-group">
                      <label htmlFor="button-message">Email Message</label>
                      <textarea
                        id="button-message"
                        className="input-v2"
                        value={data.buttonMessage || `A service request has been submitted for ${data.name}.`}
                        onChange={(e) => handleButtonConfigChange('buttonMessage', e.target.value)}
                        placeholder="Message to include in the service request email"
                        rows={8}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="ph-button-preview">
                  <div className="ph-preview-title">Button Preview</div>
                  <button 
                    className="ph-preview-button"
                    disabled={!isServiceButtonConfigured}
                  >
                    <span className="ph-button-icon">
                      {serviceIcon}
                    </span>
                    <span>{data.buttonText || 'Request Service'}</span>
                  </button>
                  {!isServiceButtonConfigured && (
                    <div className="ph-preview-warning">
                      Required fields must be filled to enable the button
                    </div>
                  )}
                </div>
                
                <div className="ph-switch-container">
                  <div className="ph-switch-text">
                    <div className="ph-switch-title">Enable Service Button</div>
                    <div className="ph-switch-subtitle">
                      Show the service request button on product page
                    </div>
                  </div>
                  <label className="ph-switch">
                    <input
                      type="checkbox"
                      checked={data.buttonEnabled !== false}
                      onChange={(e) => handleButtonConfigChange('buttonEnabled', e.target.checked)}
                      disabled={!isServiceButtonConfigured}
                    />
                    <span className="ph-slider ph-round" />
                  </label>
                </div>
                
                {/* Add Contact Form Toggle checkbox */}
                <div className="ph-switch-container" style={{ marginTop: '20px' }}>
                  <div className="ph-switch-text">
                    <div className="ph-switch-title">Show Contact Form</div>
                    <div className="ph-switch-subtitle">
                      When enabled, shows a form that collects user information
                    </div>
                  </div>
                  <label className="ph-switch">
                    <input
                      type="checkbox"
                      checked={data.showContactForm !== false}
                      onChange={(e) => handleButtonConfigChange('showContactForm', e.target.checked)}
                      disabled={!isServiceButtonConfigured}
                    />
                    <span className="ph-slider ph-round" />
                  </label>
                </div>
              </div>
            ) : (
              /* View mode content */
              data.buttonText && (
                <div className="ph-service-button-container">
                  <ServiceButton
                    buttonConfig={{
                      buttonText: data.buttonText,
                      buttonEmail: data.buttonEmail,
                      buttonSubject: data.buttonSubject,
                      buttonMessage: data.buttonMessage,
                      buttonEnabled: data.buttonEnabled,
                      showContactForm: data.showContactForm
                    }}
                    productName={data.name}
                    serialNumber={data.serialNumber}
                    onSubmit={handleServiceRequest}
                    isLoading={serviceButtonStatus.loading}
                  />
                </div>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};