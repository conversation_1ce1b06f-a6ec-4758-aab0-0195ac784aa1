import React, { useState } from 'react';
import { ToggleSwitch } from '../ToggleSwitch';
import Skeleton from 'react-loading-skeleton';

export const StatusLogsModule = ({ 
  data, 
  isLoading, 
  isEditing,
  onStatusChange,
  onDownloadScans,
  onDownloadLogs 
}) => {
  const [activeTab, setActiveTab] = useState('Status');

  if (isLoading) {
    return (
      <div className="product-module">
        <Skeleton height={40} style={{ marginBottom: '10px' }} />
        <Skeleton height={20} count={3} style={{ marginBottom: '5px' }} />
      </div>
    );
  }
  
  // Find custom status by name
  const getCustomStatus = (statusName) => {
    if (!data.customStatuses || !Array.isArray(data.customStatuses) || !statusName) {
      return null;
    }
    
    // Try exact match
    let status = data.customStatuses.find(s => 
      s.name.toLowerCase() === statusName.toLowerCase()
    );
    
    // Try matching by ID if name match fails
    if (!status) {
      const sanitizedName = statusName.toLowerCase().replace(/\s+/g, '-');
      status = data.customStatuses.find(s => s.id === sanitizedName);
    }
    
    return status;
  };

  // Determine status color based on custom statuses or fallback to default classes
  const getStatusStyle = (statusName) => {
    const customStatus = getCustomStatus(statusName);
    
    if (customStatus) {
      // Return custom styling
      return {
        backgroundColor: `${customStatus.color}20`,
        color: customStatus.color,
        borderColor: `${customStatus.color}40`
      };
    }
    
    // Fallback to default color classes if no custom status found
    switch(statusName) {
      case 'Out of Service':
      case 'Pending Repair':
      case 'Cancelled':
        return { 
          backgroundColor: '#ffebee',
          color: '#e53935',
          borderColor: '#ffcdd2'
        };
      case 'In Progress':
      case 'Initiated':
      case 'On Hold':
        return { 
          backgroundColor: '#fff8e1',
          color: '#f57f17',
          borderColor: '#ffecb3'
        };
      case 'In Service':
      case 'Completed':
        return { 
          backgroundColor: '#e8f5e9',
          color: '#2e7d32',
          borderColor: '#c8e6c9'
        };
      default:
        return { 
          backgroundColor: '#f5f5f5',
          color: '#616161',
          borderColor: '#e0e0e0'
        };
    }
  };

  return (
    <div className="product-module">
      <ToggleSwitch 
        options={['Status', 'Logs', 'Scans']} 
        onChange={setActiveTab}
        activeOption={activeTab}
        specialRounded={['Status', 'Logs', 'Scans']}
      />
      
      {/* Each tab content is rendered based on activeTab state */}
      <div className={`tab-content-container ${activeTab}`}>
        {activeTab === 'Status' && (
          <div className="settings-input-group">
            <div className="form-group">
              <div className="settings-input-group-h4">Current Status</div>
              {isEditing ? (
                <select 
                  className="input"
                  value={data.status || 'Initiated'}
                  onChange={(e) => onStatusChange(e.target.value)}
                >
                  {/* Use custom statuses if available, otherwise fallback to defaults */}
                  {data.customStatuses && data.customStatuses.length > 0 ? (
                    data.customStatuses.map(status => (
                      <option key={status.id} value={status.name}>{status.name}</option>
                    ))
                  ) : (
                    <>
                      <option value="Initiated">Initiated</option>
                      <option value="In Progress">In Progress</option>
                      <option value="On Hold">On Hold</option>
                      <option value="Completed">Completed</option>
                      <option value="Cancelled">Cancelled</option>
                    </>
                  )}
                </select>
              ) : (
                <div 
                  className="status-indicator" 
                  style={getStatusStyle(data.status)}
                >
                  {data.status || 'Initiated'}
                </div>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'Logs' && (
          <div className="settings-input-group">
            <div className="form-group">
              <div className="settings-input-group-h4">Status History</div>
              {data.logs && data.logs.length > 0 ? (
                <>
                  <div className="toggle-div-collapsable-1">
                    {data.logs.map((log, index) => (
                      <div key={index} className="detail-row">
                        <span className="detail-label">{formatDateTime(log.timestamp)}</span>
                        <div>
                          <span 
                            className="status-indicator" 
                            style={getStatusStyle(log.status)}
                          >
                            {log.status}
                          </span>
                          {log.message && (
                            <span className="toggle-title-body" style={{marginLeft: '8px'}}>
                              {log.message}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  {onDownloadLogs && (
                    <button 
                      className="settings-footer-button"
                      onClick={onDownloadLogs}
                      style={{ marginTop: '16px' }}
                    >
                      Download as Excel
                    </button>
                  )}
                </>
              ) : (
                <div className="empty-state">No status history available</div>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'Scans' && (
          <div className="settings-input-group">
            <div className="form-group">
              <div className="settings-input-group-h4">Scan History</div>
              {isLoading ? (
                <div className="loading-scans">
                  <Skeleton height={20} count={3} style={{ marginBottom: '5px' }} />
                </div>
              ) : data.scans && data.scans.length > 0 ? (
                <>
                  <div className="toggle-div-collapsable-1">
                    {data.scans.map((scan, index) => (
                      <div key={index} className="detail-row">
                        <span className="detail-label">{formatDateTime(scan.timestamp)}</span>
                        <span>{scan.location || 'Unknown location'}</span>
                      </div>
                    ))}
                  </div>
                  <button 
                    className="settings-footer-button"
                    onClick={onDownloadScans}
                    style={{ marginTop: '16px' }}
                  >
                    Download as Excel
                  </button>
                </>
              ) : (
                <div className="empty-state">No scan history available</div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to format date and time
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};