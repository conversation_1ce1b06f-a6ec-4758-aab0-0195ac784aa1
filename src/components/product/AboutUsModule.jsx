import React, { useState } from 'react';
import Skeleton from 'react-loading-skeleton';

export const AboutUsModule = ({
  data,
  isLoading,
  isEditing,
  onAboutUsChange,
  viewMode = 'admin' // Add viewMode prop for consistency with other modules
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // SVG for arrow pointing down (to expand)
  const downArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 9.5L1 4.5L2 3.5L6 7.5L10 3.5L11 4.5L6 9.5Z" fill="currentColor"/>
    </svg>
  );

  // SVG for arrow pointing up (to collapse)
  const upArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 2.5L11 7.5L10 8.5L6 4.5L2 8.5L1 7.5L6 2.5Z" fill="currentColor"/>
    </svg>
  );

  if (isLoading || data.aboutUsEnabled === false) {
    return isLoading ? (
      <div className="settings-input-group-two">
        <Skeleton height={40} width={150} style={{ marginBottom: '10px' }} />
        <Skeleton height={20} count={4} width="90%" style={{ marginBottom: '8px' }} />
      </div>
    ) : null;
  }

  // Render the header based on viewMode
  const renderHeader = () => {
    if (viewMode === 'admin' || isEditing) {
      return (
        <div 
          className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
          onClick={isEditing ? toggleExpand : undefined}
          style={isEditing ? { cursor: 'pointer' } : {}}
        >
          <div className="toggle-text-container-parent">
            <div className="link-primary-icon">
              <img
                src="/about_us.svg"
                alt="About Us"
                width="16"
                height="16"
                onError={(e) => {e.target.style.display = 'none'}}
              />
            </div>
            <div className="toggle-text-container">
              <div className="settings-input-group-h4">About Us</div>
              {isEditing && (
                <div className="settings-input-group-h4-sub">
                  Company information displayed on your product page
                </div>
              )}
            </div>
          </div>
          
          {isEditing && (
            <div className="collapse-icon-container">
              <div className="collapse-icon">
                {isExpanded ? upArrowSvg : downArrowSvg}
              </div>
            </div>
          )}
        </div>
      );
    } else {
      // View mode with expand/collapse arrow
      return (
        <div 
          className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
          onClick={toggleExpand}
          style={{ cursor: 'pointer' }}
        >
          <div className="toggle-text-container-parent">
            <div className="link-primary-icon">
              <img
                src="/about_us.svg"
                alt="About Us"
                width="16"
                height="16"
                onError={(e) => {e.target.style.display = 'none'}}
              />
            </div>
            <div className="toggle-text-container">
              <div className="settings-input-group-h4">About Us</div>
              <div className="settings-input-group-h4-sub">
                Company information
              </div>
            </div>
          </div>
          <div className="collapse-icon-container">
            <div className="collapse-icon">
              {isExpanded ? upArrowSvg : downArrowSvg}
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className={`settings-input-group-two ${viewMode === 'view' ? 'viewer-mode' : ''}`}>
      {renderHeader()}
      
      {/* Use location-collapse-container for smooth animation */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {isEditing ? (
          <div className="settings-section-content">
            <div className="input-group">
              <label htmlFor="company-name">Company Name</label>
              <div className="email-input-container">
                <input
                  id="company-name"
                  className="input-v2"
                  value={data.companyName || ''}
                  onChange={(e) => onAboutUsChange('companyName', e.target.value)}
                  placeholder="Your company name"
                />
                {data.companyName && (
                  <button 
                    className="clear-input-button" 
                    onClick={() => onAboutUsChange('companyName', '')}
                    type="button"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
            
            <div className="input-group">
              <label htmlFor="company-website">Website</label>
              <div className="email-input-container">
                <input
                  id="company-website"
                  className="input-v2"
                  value={data.website || ''}
                  onChange={(e) => onAboutUsChange('website', e.target.value)}
                  placeholder="https://example.com"
                />
                {data.website && (
                  <button 
                    className="clear-input-button" 
                    onClick={() => onAboutUsChange('website', '')}
                    type="button"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
            
            <div className="input-group">
              <label htmlFor="company-phone">Phone Number</label>
              <div className="email-input-container">
                <input
                  id="company-phone"
                  className="input-v2"
                  value={data.phoneNumber || ''}
                  onChange={(e) => onAboutUsChange('phoneNumber', e.target.value)}
                  placeholder="Enter phone number"
                />
                {data.phoneNumber && (
                  <button 
                    className="clear-input-button" 
                    onClick={() => onAboutUsChange('phoneNumber', '')}
                    type="button"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
            
            <div className="input-group">
              <label htmlFor="company-email">Email</label>
              <div className="email-input-container">
                <input
                  id="company-email"
                  className="input-v2"
                  value={data.contactEmail || ''}
                  onChange={(e) => onAboutUsChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
                {data.contactEmail && (
                  <button 
                    className="clear-input-button" 
                    onClick={() => onAboutUsChange('contactEmail', '')}
                    type="button"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>

            <div style={{ marginTop: '20px' }}>
              <div style={{ marginTop: '10px', display: 'flex', alignItems: 'center' }}>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={data.aboutUsEnabled !== false}
                    onChange={(e) => onAboutUsChange('aboutUsEnabled', e.target.checked)}
                  />
                  <span className="slider round"></span>
                </label>
                <span style={{ marginLeft: '12px', fontSize: '14px' }}>
                  Display company information on product page
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="toggle-div-collapsable-1" style={{ padding: '0', paddingBottom: '8px' }}>
            {data.companyName && (
              <div className="detail-row" style={{ 
                padding: '8px 0',
                borderBottom: '1px solid #dedede',
                marginBottom: '8px' 
              }}>
                <span className="detail-label">Company:</span>
                <span style={{ color: '#111827', fontWeight: '500' }}>{data.companyName}</span>
              </div>
            )}
            
            {data.website && (
              <div className="detail-row" style={{ 
                padding: '8px 0',
                borderBottom: '1px solid #dedede',
                marginBottom: '8px' 
              }}>
                <span className="detail-label">Website:</span>
                <a 
                  href={data.website.startsWith('http') ? data.website : `https://${data.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="link-primary"
                >
                  {data.website}
                </a>
              </div>
            )}
            
            {data.phoneNumber && (
              <div className="detail-row" style={{ 
                padding: '8px 0',
                borderBottom: '1px solid #dedede',
                marginBottom: '8px' 
              }}>
                <span className="detail-label">Phone:</span>
                <a href={`tel:${data.phoneNumber}`} style={{ color: '#111827', fontWeight: '500' }}>
                  {data.phoneNumber}
                </a>
              </div>
            )}
            
            {data.contactEmail && (
              <div className="detail-row" style={{ 
                padding: '8px 0',
                ...((!data.companyName && !data.website && !data.phoneNumber) ? {} : {
                  borderBottom: '1px solid #dedede',
                  marginBottom: '8px'
                })
              }}>
                <span className="detail-label">Email:</span>
                <a 
                  href={`mailto:${data.contactEmail}`} 
                  className="link-primary"
                >
                  {data.contactEmail}
                </a>
              </div>
            )}
            
            {!data.companyName && !data.website && !data.phoneNumber && !data.contactEmail && (
              <div className="empty-state">No company information available</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};