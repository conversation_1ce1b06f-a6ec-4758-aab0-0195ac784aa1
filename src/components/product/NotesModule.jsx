import React, { useState } from 'react';
import Skeleton from 'react-loading-skeleton';

export const NotesModule = ({
  data,
  isLoading,
  isEditing,
  onNotesChange,
  viewMode = 'admin' // Add viewMode prop to match ManageAssetDocuments
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Toggle section expand/collapse in viewer mode
  const toggleSectionExpand = () => {
    setIsExpanded(!isExpanded);
  };

  if (isLoading) {
    return (
      <div className="settings-input-group-two">
        <Skeleton height={40} width={150} style={{ marginBottom: '10px' }} />
        <Skeleton height={20} count={3} width="90%" style={{ marginBottom: '8px' }} />
      </div>
    );
  }

  // If notes aren't enabled or no notes content exists, don't render anything
  if (!data.notesEnabled || !data.notes) {
    return null;
  }

  // SVG for arrow pointing down (to expand)
  const downArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 9.5L1 4.5L2 3.5L6 7.5L10 3.5L11 4.5L6 9.5Z" fill="currentColor"/>
    </svg>
  );

  // SVG for arrow pointing up (to collapse)
  const upArrowSvg = (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 2.5L11 7.5L10 8.5L6 4.5L2 8.5L1 7.5L6 2.5Z" fill="currentColor"/>
    </svg>
  );

  // Render the header based on viewMode
  const renderHeader = () => {
    if (viewMode === 'admin' || isEditing) {
      return (
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/custom_notes.svg"
              alt="Notes"
              width="16"
              height="16"
              onError={(e) => {e.target.style.display = 'none'}}
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">Notes</div>
            {isEditing && (
              <div className="settings-input-group-h4-sub">
                Documentation for authenticated users
              </div>
            )}
          </div>
        </div>
      );
    } else {
      // View mode - show expand/collapse arrow
      return (
        <div 
          className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
          onClick={toggleSectionExpand}
          style={{ cursor: 'pointer' }}
        >
          <div className="toggle-text-container-parent">
            <div className="link-primary-icon">
              <img
                src="/custom_notes.svg"
                alt="Notes"
                width="16"
                height="16"
                onError={(e) => {e.target.style.display = 'none'}}
              />
            </div>
            <div className="toggle-text-container">
              <div className="settings-input-group-h4">Notes</div>
              <div className="settings-input-group-h4-sub">
                View notes for this asset
              </div>
            </div>
          </div>
          <div className="collapse-icon-container">
            <div className="collapse-icon">
              {isExpanded ? upArrowSvg : downArrowSvg}
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className={`settings-input-group-two ${viewMode === 'view' ? 'viewer-mode' : ''}`}>
      {renderHeader()}
      
      {/* Use location-collapse-container for smooth animation, same as other components */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {isEditing ? (
          <div className="settings-section-content">
            <div className="input-group">
              <textarea
                id="notes-content"
                className="input-v2"
                value={data.notes || ''}
                onChange={(e) => onNotesChange('notes', e.target.value)}
                placeholder="Enter notes for authenticated users..."
                rows={4}
                style={{ 
                  resize: 'vertical',
                  minHeight: '120px', 
                  marginBottom: '0'
                }}
              />
            </div>
          </div>
        ) : (
          <div className="document-list" style={{ 
            margin: '15px 0 0',
            maxHeight: 'none',
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            padding: '12px 15px',
            backgroundColor: 'white' 
          }}>
            {data.notes.split('\n').map((line, i) => (
              line ? (
                <p key={i} className="toggle-title-body" style={{ 
                  margin: '0 0 8px 0',
                  lineHeight: '1.4',
                  color: '#333'
                }}>
                  {line}
                </p>
              ) : <div key={i} style={{ height: '12px' }} />
            ))}
            
            {!data.notes.trim() && (
              <div className="no-documents-message">
                No notes available.
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};