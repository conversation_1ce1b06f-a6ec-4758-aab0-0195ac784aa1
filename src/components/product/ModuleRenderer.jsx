import React from 'react';
import { Header } from './Header';
import { StatusLogsModule } from './StatusLogsModule';
import { AboutUsModule } from './AboutUsModule';
import { NotesModule } from './NotesModule';
import ManageAssetDocuments from '../storefront/ManageAssetDocuments';

export const ModuleRenderer = ({ 
  module, 
  assetData, 
  isLoading,
  isEditing = false,
  onSerialChange,
  onImageUpload,
  onButtonConfigChange,
  onRequestButtonClick,
  onStatusChange,
  onDownloadScans,
  onDownloadLogs,
  onAboutUsChange,
  getAuthHeaders,
  onNameChange,
  onViewDocument,
  onNotesChange,
  onUserNoteSubmit,
  isEmailSending
}) => {
  if (!assetData) return null;
  
  // Helper function to check if a string has actual content
  const hasContent = (str) => {
    return str && typeof str === 'string' && str.trim().length > 0;
  };
  
  switch (module.type) {
    case 'header':
      return (
        <Header 
          key={module.id} 
          data={{
            name: assetData.name,
            // Only include description if it has content
            description: hasContent(assetData.description) ? assetData.description : null,
            address: assetData.address,
            // Only include serial number if it has content
            serialNumber: hasContent(assetData.serialNumber) ? assetData.serialNumber : null,
            imageUrl: assetData.imageUrl,
            buttonText: assetData.buttonText,
            buttonEmail: assetData.buttonEmail,
            buttonSubject: assetData.buttonSubject,
            buttonMessage: assetData.buttonMessage,
            buttonEnabled: assetData.buttonEnabled,
            showContactForm: assetData.showContactForm, // Added this critical property
            imageEnabled: assetData.imageEnabled
          }}
          isLoading={isLoading}
          isEditing={isEditing}
          onSerialChange={onSerialChange}
          onImageUpload={onImageUpload}
          onButtonConfigChange={onButtonConfigChange}
          onRequestButtonClick={onRequestButtonClick}
          onNameChange={onNameChange}
          isEmailSending={isEmailSending}
        />
      );
    case 'statusLogs':
      return (
        <StatusLogsModule
          key={module.id}
          data={{
            status: assetData.status,
            logs: assetData.logs || [],
            scans: assetData.scans || [],
            customStatuses: assetData.customStatuses || []
          }}
          isLoading={isLoading}
          isEditing={isEditing}
          onStatusChange={onStatusChange}
          onDownloadScans={onDownloadScans}
          onDownloadLogs={onDownloadLogs}
        />
      );
    case 'aboutUs':
      return (
        <AboutUsModule
          key={module.id}
          data={{
            companyName: assetData.companyName || '',
            website: assetData.website || '',
            phoneNumber: assetData.phoneNumber || '',
            contactEmail: assetData.contactEmail || '',
            aboutUsEnabled: assetData.aboutUsEnabled
          }}
          isLoading={isLoading}
          isEditing={isEditing}
          onAboutUsChange={onAboutUsChange}
          viewMode="view"
        />
      );
    case 'notes':
      return (
        <NotesModule
          key={module.id}
          data={{
            notes: assetData.notes || '',
            notesEnabled: assetData.notesEnabled || false
          }}
          isLoading={isLoading}
          isEditing={isEditing}
          onNotesChange={onNotesChange}
          onUserNoteSubmit={onUserNoteSubmit}
          viewMode="view"
        />
      );
    case 'documents':
      // Don't render if documents are disabled
      if (assetData.documentsEnabled === false) {
        return null;
      }
      
      return (
        <ManageAssetDocuments
          key={module.id}
          assetId={assetData.id}
          documents={assetData.documents || []}
          getAuthHeaders={getAuthHeaders}
          documentsEnabled={true} // Always true here since we check above
          storefrontEnabled={true} // Always true for product page
          viewMode="view"
          onViewDocument={onViewDocument}
        />
      );
    default:
      return null;
  }
};