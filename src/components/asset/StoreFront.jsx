import React from 'react';
import { useNavigate } from 'react-router-dom';

export function StoreFront({ assetId, enabled = false }) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/assets/${assetId}/storefront`);
  };

  return (
    <div 
      className="settings-input-group-two sf-clickable-panel"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && handleClick()}
    >
      <div className="toggle-div-collapsable-1-sub-1">
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/storefront.svg"
              alt="StoreFront"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">StoreFront</div>
            <div className="settings-input-group-h4-sub">
              Publish information for this Asset
            </div>
          </div>
        </div>
        <div className="sf-flex-center-align">
          <span className={`sf-status-badge ${enabled ? 'sf-enabled' : 'sf-disabled'}`}>
            {enabled ? 'Enabled' : 'Disabled'}
          </span>
          <img 
            src="/arrow_right.svg" 
            alt="Go to StoreFront" 
            className="back-arrow"
          />
        </div>
      </div>
    </div>
  );
}