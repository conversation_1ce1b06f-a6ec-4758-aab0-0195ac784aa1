import React, { useState } from 'react';

export function ActionButtonPanel({
  buttonConfig,
  isEnabled,
  toggleSectionEnabled,
  isStorefrontEnabled,
  shouldExpandPanel,
  optionalLabelStyle,
  onButtonConfigChange,
  handleContactFormToggle
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle toggle expansion
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Check if the feature is truly enabled (both settings are on)
  const isFeatureEnabled = isEnabled && isStorefrontEnabled;

  return (
    <div className="settings-input-group-two">
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={handleToggleExpansion}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img
              src="/service_button.svg"
              alt="Service Button"
              width="16"
              height="16"
            />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              Service Button
              {!isStorefrontEnabled && (
                <span style={optionalLabelStyle}>
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Set action button for this Asset
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        {/* First divider */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {/* Enable switch row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '12px'
        }}>
          <div className="settings-input-group-h4-sub" style={{ fontWeight: 500 }}>
          </div>
          <label className={`switch ${!isStorefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={isEnabled === true}
              onChange={() => toggleSectionEnabled('actionButton')}
              disabled={!isStorefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
        
        {/* Second divider - only show when features are enabled */}
        {isEnabled && isStorefrontEnabled && (
          <div
            style={{
              borderTop: '1px solid #E5E7EB',
              margin: '0 0 12px 0',
            }}
          />
        )}
        
        {/* Form fields - only show when features are enabled */}
        {isEnabled && isStorefrontEnabled && (
          <>
            <div className="input-group">
              <label htmlFor="button-text">Button Text</label>
              <input
                id="button-text"
                className="input"
                value={buttonConfig.buttonText}
                onChange={(e) => onButtonConfigChange('buttonText', e.target.value)}
                placeholder="e.g., Request Service"
              />
            </div>
            
            <div className="input-group">
              <label htmlFor="button-email">Recipient Email</label>
              <input
                id="button-email"
                className="input"
                value={buttonConfig.buttonEmail}
                onChange={(e) => onButtonConfigChange('buttonEmail', e.target.value)}
                placeholder="e.g., <EMAIL>"
              />
            </div>
            
            <div className="input-group">
              <label htmlFor="button-subject">Email Subject</label>
              <input
                id="button-subject"
                className="input"
                value={buttonConfig.buttonSubject}
                onChange={(e) => onButtonConfigChange('buttonSubject', e.target.value)}
                placeholder="e.g., Service Request for Asset"
              />
            </div>
            
            <div className="input-group">
              <label htmlFor="button-message">Email Message</label>
              <textarea
                id="button-message"
                className="input"
                rows="3"
                value={buttonConfig.buttonMessage}
                onChange={(e) => onButtonConfigChange('buttonMessage', e.target.value)}
                placeholder="Message to include in the service request email"
              />
            </div>
            
            {/* Contact Form Checkbox */}
            <div className="input-group" style={{ marginTop: '20px' }}>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: '10px' }}>
                <input
                  type="checkbox"
                  id="contact-form-checkbox"
                  checked={buttonConfig.showContactForm !== false}
                  onChange={handleContactFormToggle}
                  className="checkbox-input"
                  style={{ marginTop: '4px' }}
                />
                <div>
                  <label htmlFor="contact-form-checkbox" style={{ cursor: 'pointer', fontWeight: 'bold' }}>Contact Form</label>
                  <div className="settings-input-group-h4-sub">
                    Allow users input and contact details
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}