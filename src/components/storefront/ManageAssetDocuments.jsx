import React, { useState, useCallback } from 'react';
import '../../styles/ManageAssetDocuments.css';
import '../../styles/ProductHeader.css'; // Import shared styles for consistency
import { API, Auth } from 'aws-amplify';

// Custom header name - must match the backend
const AUTH_TOKEN_HEADER = 'X-Auth-Token';

export default function ManageAssetDocuments({ 
  assetId, 
  documents = [], 
  getAuthHeaders, 
  onDocumentUpdated,
  documentsEnabled = true, 
  onDocumentsEnabledChange,
  storefrontEnabled = true, // For checking if storefront is enabled
  viewMode = 'admin' // New prop to determine display mode: 'admin' or 'view'
}) {
  const [isUploading, setIsUploading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [expandedDocId, setExpandedDocId] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [authToken, setAuthToken] = useState(null);
  const [isExpanded, setIsExpanded] = useState(true); // For viewer mode collapse/expand

  // Handle toggle for documents feature
  const handleToggleDocuments = (e) => {
    e.stopPropagation(); // Prevent panel toggle when clicking the switch
    
    // Only allow toggling if storefront is enabled
    if (storefrontEnabled && onDocumentsEnabledChange) {
      onDocumentsEnabledChange(e.target.checked);
    }
  };

  // Toggle section expand/collapse in viewer mode
  const toggleSectionExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Handle file selection and upload
  const handleFileSelect = useCallback(async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    const fileExt = file.name.split('.').pop().toLowerCase();
    const supportedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];
    
    if (!supportedTypes.includes(fileExt)) {
      setErrorMessage(`Unsupported file type. Please upload: ${supportedTypes.join(', ')}`);
      return;
    }

    // Check file size (20MB max)
    const MAX_SIZE = 20 * 1024 * 1024;
    if (file.size > MAX_SIZE) {
      setErrorMessage('File is too large. Maximum size is 20MB.');
      return;
    }

    setErrorMessage('');
    setIsUploading(true);

    try {
      // Read file as base64
      const reader = new FileReader();
      
      reader.onload = async (event) => {
        const base64Data = event.target.result;
        
        // Prepare API request
        const headers = await getAuthHeaders();
        const body = {
          document: file.name,
          documentData: base64Data,
          accessLevel: 'private' // Default new documents to private
        };

        // Upload the document
        const result = await API.post('api', `/assets/${assetId}/documents`, {
          headers,
          body
        });

        // Update document list
        if (result && result.document) {
          if (onDocumentUpdated) {
            onDocumentUpdated([result.document, ...documents]);
          }
        }
        
        setIsUploading(false);
      };

      reader.onerror = () => {
        setErrorMessage('Error reading file. Please try again.');
        setIsUploading(false);
      };

      reader.readAsDataURL(file);
      
    } catch (error) {
      console.error('Error uploading document:', error);
      setErrorMessage(`Upload failed: ${error.message || 'Unknown error'}`);
      setIsUploading(false);
    }
  }, [assetId, documents, getAuthHeaders, onDocumentUpdated]);

  // Handle document removal
  const handleRemoveDocument = useCallback(async (documentId) => {
    try {
      const headers = await getAuthHeaders();
      await API.del('api', `/assets/${assetId}/documents/${documentId}`, { headers });
      
      // Update local document list
      if (onDocumentUpdated) {
        onDocumentUpdated(documents.filter(doc => doc.id !== documentId));
      }
      
      // Close preview if the removed document was expanded
      if (expandedDocId === documentId) {
        setExpandedDocId(null);
        setPreviewUrl(null);
      }
    } catch (error) {
      console.error('Error removing document:', error);
      setErrorMessage(`Failed to remove document: ${error.message || 'Unknown error'}`);
    }
  }, [assetId, documents, getAuthHeaders, onDocumentUpdated, expandedDocId]);

  // Handle access level change
  const handleAccessLevelChange = useCallback(async (documentId, newAccessLevel) => {
    try {
      const headers = await getAuthHeaders();
      
      // Update the document's access level
      await API.post('api', `/assets/${assetId}/documents/${documentId}`, {
        headers,
        body: { accessLevel: newAccessLevel }
      });
      
      // Update the document list locally
      if (onDocumentUpdated) {
        const updatedDocuments = documents.map(doc => 
          doc.id === documentId ? { ...doc, accessLevel: newAccessLevel } : doc
        );
        onDocumentUpdated(updatedDocuments);
      }
    } catch (error) {
      console.error('Error updating document access level:', error);
      setErrorMessage(`Failed to update access level: ${error.message || 'Unknown error'}`);
    }
  }, [assetId, documents, getAuthHeaders, onDocumentUpdated]);

  const safelyUpdateIdToken = async () => {
    try {
      // This will throw an error if not authenticated
      const session = await Auth.currentSession();
      const idToken = session.getIdToken().getJwtToken();
      
      // Only update if we have a valid token
      if (idToken) {
        // Store the token in state for use in request headers
        setAuthToken(idToken);
      }
      return true; // User is authenticated
    } catch (err) {
      // User is not authenticated - this is not an error case
      console.log('User not authenticated, proceeding as guest');
      return false; // User is not authenticated
    }
  };   

  // Fetch document data for preview
  const fetchDocumentPreview = useCallback(async (documentId) => {
    if (!documentId) return;
    
    setIsLoadingPreview(true);
    try {
      // Ensure the token is updated before making the API call
      await safelyUpdateIdToken(); 
      
      // Get the current session to extract the ID token
      let requestHeaders = {};
      
      try {
        const session = await Auth.currentSession();
        const idToken = session.getIdToken().getJwtToken();
        
        // Add the custom auth header
        requestHeaders[AUTH_TOKEN_HEADER] = idToken;
      } catch (err) {
        console.log('No authenticated session, proceeding as guest');
      }
      
      // Fetch document metadata from API with token in header only
      const response = await API.get('public-api', `/assets/${assetId}/documents/${documentId}`, {
        // Add our custom header
        headers: requestHeaders
      });
    
      if (response && response.URL) {
        setPreviewUrl(response.URL);
      } else {
        setErrorMessage('Could not load document preview');
      }
    } catch (error) {
      console.error('Error fetching document preview:', error);
      setErrorMessage('Failed to load document preview');
    } finally {
      setIsLoadingPreview(false);
    }
  }, [assetId]);

  // Toggle document expansion
  const toggleDocumentExpand = useCallback((documentId) => {
    if (expandedDocId === documentId) {
      // Collapse the document
      setExpandedDocId(null);
      setPreviewUrl(null);
    } else {
      // Expand the document and fetch preview
      setExpandedDocId(documentId);
      fetchDocumentPreview(documentId);
      
      // Increment view count
      const updatedDocuments = documents.map(doc => {
        if (doc.id === documentId) {
          const currentViews = doc.views || 0;
          return { ...doc, views: currentViews + 1 };
        }
        return doc;
      });
      
      if (onDocumentUpdated) {
        onDocumentUpdated(updatedDocuments);
      }
    }
  }, [expandedDocId, documents, fetchDocumentPreview, onDocumentUpdated]);

  // Handle view document in new tab
  const handleViewDocument = useCallback((documentId) => {
    // Open document in new tab
    const cookieDomain = import.meta.env.VITE_COOKIE_DOMAIN;
    let url;
    if (cookieDomain === 'localhost') {
      url = `http://localhost:5173/assets/${assetId}/documents/${documentId}`;
    } else {
      url = `https://thinkertags.com/assets/${assetId}/documents/${documentId}`;
    }
    
    window.open(url, '_blank');
    
    // Optimistically update the view count in the UI
    if (onDocumentUpdated) {
      const updatedDocuments = documents.map(doc => {
        if (doc.id === documentId) {
          // Increment the views count or initialize at 1 if it doesn't exist
          const currentViews = doc.views || 0;
          return { ...doc, views: currentViews + 1 };
        }
        return doc;
      });
      
      // Update the document list locally
      onDocumentUpdated(updatedDocuments);
    }
  }, [assetId, documents, onDocumentUpdated]);

  // Get file extension from filename
  const getFileExtension = (fileName) => {
    if (!fileName) return '';
    return fileName.split('.').pop().toLowerCase();
  };

  // Check if file is an image
  const isImageFile = (fileName) => {
    const ext = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif'].includes(ext);
  };

  // Check if file is a PDF
  const isPdfFile = (fileName) => {
    return getFileExtension(fileName) === 'pdf';
  };

  // Render document actions based on viewMode
  const renderDocumentActions = (doc) => {
    if (viewMode === 'admin') {
      // Admin mode - show all actions
      return (
        <div className="document-actions">
          <select
            className="document-access-level"
            value={doc.accessLevel || 'private'}
            onChange={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              handleAccessLevelChange(doc.id, e.target.value);
            }}
            onClick={(e) => e.stopPropagation()} // Prevent triggering the parent onClick
            title="Set document visibility"
          >
            <option value="private">Private</option>
            <option value="public">Public</option>
          </select>
          <button
            className="document-action-button"
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              handleViewDocument(doc.id, doc.name);
            }}
            title="Open document in new tab"
          >
            <img src="/view_document.svg" alt="Preview" width="18" height="18" />
          </button>
          <button
            className="document-action-button remove"
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              handleRemoveDocument(doc.id);
            }}
            title="Remove document"
          >
            <img src="/remove.svg" alt="Remove" width="18" height="18" />
          </button>
        </div>
      );
    } else {
      // View mode - show only view button
      return (
        <div className="document-actions viewer-mode">
          <button
            className="document-action-button"
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              handleViewDocument(doc.id, doc.name);
            }}
            title="Open document in new tab"
          >
            <img src="/view_document.svg" alt="Preview" width="18" height="18" />
          </button>
        </div>
      );
    }
  };

  // Main render function
  return (
    <div className={`settings-input-group-two ${viewMode === 'view' ? 'viewer-mode' : ''}`}>
      {viewMode === 'admin' ? (
        // Admin mode - show toggle switch without collapse arrows
        <div className="toggle-div-collapsable-1-sub-1">
          <div className="toggle-text-container-parent">
            <div className="link-primary-icon">
              <img src="/documents.svg" alt="Documents" width="16" height="16" />
            </div>
            <div className="toggle-text-container">
              <div className="settings-input-group-h4">
                Asset Documents
                {!storefrontEnabled && (
                  <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal', marginLeft: '5px' }}>
                    (Requires StoreFront)
                  </span>
                )}
              </div>
              <div className="settings-input-group-h4-sub">
                {documentsEnabled ? 'Visible on product page' : 'Hidden on product page'}
              </div>
            </div>
          </div>
          <label className={`switch ${!storefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={documentsEnabled}
              onChange={handleToggleDocuments}
              disabled={!storefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
      ) : (
        // View mode - show only title with collapse functionality
        <div 
          className="toggle-div-collapsable-1-sub-1 viewer-mode-header"
          onClick={toggleSectionExpand}
          style={{ cursor: 'pointer' }}
        >
          <div className="toggle-text-container-parent">
            <div className="link-primary-icon">
              <img src="/documents.svg" alt="Documents" width="16" height="16" />
            </div>
            <div className="toggle-text-container">
              <div className="settings-input-group-h4">Documents</div>
              <div className="settings-input-group-h4-sub">
                View documents for this asset
              </div>
            </div>
          </div>
          <div className="expand-collapse-icon">
            <img 
              src={isExpanded ? "/expand_down.svg" : "/expand_up.svg"} 
              alt={isExpanded ? "Collapse" : "Expand"} 
              width="20" 
              height="20" 
            />
          </div>
        </div>
      )}

      {/* Always render the container, but control with expanded class for smooth animation */}
      <div className={`location-collapse-container ${documentsEnabled && (storefrontEnabled || viewMode === 'view') && (viewMode === 'admin' || isExpanded) ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        {/* File upload area - only show in admin mode */}
        {viewMode === 'admin' && (
          <div className="document-upload-area">
            <label className={`document-upload-button ${isUploading ? 'uploading' : ''}`} htmlFor="document-upload">
              {isUploading ? 'Uploading...' : 'Upload Document'}
              <input
                id="document-upload"
                type="file"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                onChange={handleFileSelect}
                disabled={isUploading}
                style={{ display: 'none' }}
              />
            </label>
            
            {errorMessage && (
              <div className="document-error-message">{errorMessage}</div>
            )}
          </div>
        )}

        {/* Document list - sorted by views */}
        {documents && documents.length > 0 ? (
          <div className="document-list">
            {documents
              .slice() // Create a copy to avoid mutating the original array
              .sort((a, b) => (b.views || 0) - (a.views || 0)) // Sort by views in descending order
              .map((doc) => (
                <div key={doc.id} className={`document-list-item ${expandedDocId === doc.id ? 'expanded' : ''}`}>
                  <div 
                    className="document-item-header"
                    onClick={() => toggleDocumentExpand(doc.id)}
                  >
                    <div className="document-info">
                      <div className="document-name">{doc.name}</div>
                      <div className="document-date">
                        <img src="/views.svg" alt="Views" width="12" height="12" />
                        Views: {doc.views || 0}
                      </div>
                    </div>
                    {renderDocumentActions(doc)}
                  </div>
                  
                  {/* Expanded preview section */}
                  {expandedDocId === doc.id && (
                    <div className="document-preview-container">
                      {isLoadingPreview ? (
                        <div className="document-preview-loading">Loading preview...</div>
                      ) : previewUrl ? (
                        <div className="document-preview">
                          {isImageFile(doc.name) ? (
                            <img src={previewUrl} alt={doc.name} className="preview-image" />
                          ) : isPdfFile(doc.name) ? (
                            <iframe 
                              src={previewUrl} 
                              title={doc.name}
                              className="preview-pdf"
                              frameBorder="0"
                            />
                          ) : (
                            <div className="preview-unsupported">
                              <p>Preview not available for this file type.</p>
                              <button 
                                className="preview-download-button"
                                onClick={() => handleViewDocument(doc.id)}
                              >
                                Open document in new tab
                              </button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="document-preview-error">
                          Could not load preview. Try opening in a new tab.
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
          </div>
        ) : (
          <div>
          </div>
        )}
      </div>
    </div>
  );
}