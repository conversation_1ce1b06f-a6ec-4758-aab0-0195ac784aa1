import React, { useState } from 'react';

export function CustomNotes({
  notes = '',
  notesEnabled = false,
  storefrontEnabled = false,
  onNotesChange,
  onNotesEnabledChange
}) {
  // Track expansion state separately from enabled state
  const [isExpanded, setIsExpanded] = useState(false);

  // Helper style for optional field labels
  const optionalLabelStyle = { 
    fontSize: '12px', 
    color: '#666', 
    fontWeight: 'normal', 
    marginLeft: '5px' 
  };

  // Handle toggling notes on/off
  const handleToggleNotes = (e) => {
    e.stopPropagation(); // Prevent event bubbling
    const isEnabled = e.target.checked;
    
    // Only allow toggling if storefront is enabled
    if (storefrontEnabled) {
      // Update notes enabled status
      onNotesEnabledChange(isEnabled);
    }
  };
  
  // Handle note content changes
  const handleNoteChange = (e) => {
    onNotesChange(e.target.value);
  };

  // Handle toggle expansion
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Check if the feature is truly enabled (both settings are on)
  const isFeatureEnabled = notesEnabled && storefrontEnabled;

  return (
    <div className="settings-input-group-two">
      {/* Header with collapse/expand arrow */}
      <div 
        className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}
        onClick={handleToggleExpansion}
        style={{ cursor: 'pointer' }}
      >
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img src="/custom_notes.svg" alt="Custom Notes" width="16" height="16" />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">
              Custom Notes
              {!storefrontEnabled && (
                <span style={optionalLabelStyle}>
                  (Requires StoreFront)
                </span>
              )}
            </div>
            <div className="settings-input-group-h4-sub">
              Enable private notes for authenticated users
            </div>
          </div>
        </div>
        <div className="collapse-icon-container">
          <div className="collapse-icon">
            <img
              src={isExpanded ? "/expand_up.svg" : "/expand_down.svg"}
              alt={isExpanded ? "Collapse" : "Expand"}
              className="toggle-icon"
              width="20" 
              height="20"
            />
          </div>
        </div>
      </div>

      {/* Collapsible content section */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        {/* First divider */}
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        
        {/* Enable switch row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '12px'
        }}>
          <div className="settings-input-group-h4-sub" style={{ fontWeight: 500 }}>
          </div>
          <label className={`switch ${!storefrontEnabled ? 'disabled-switch' : ''}`}>
            <input
              type="checkbox"
              checked={notesEnabled === true}
              onChange={handleToggleNotes}
              disabled={!storefrontEnabled}
            />
            <span className="slider round" />
          </label>
        </div>
        
        {/* Second divider - only show when notes are enabled */}
        {notesEnabled && storefrontEnabled && (
          <div
            style={{
              borderTop: '1px solid #E5E7EB',
              margin: '0 0 12px 0',
            }}
          />
        )}
        
        {/* Notes textarea - only show when notes are enabled */}
        {notesEnabled && storefrontEnabled && (
          <div className="input-group">
            <textarea
              id="notes-content"
              className="input"
              value={notes || ''}
              onChange={handleNoteChange}
              placeholder="Enter notes for authenticated users..."
              rows={5}
              style={{ fontFamily: 'inherit' }}
            />
          </div>
        )}
      </div>
    </div>
  );
}