import React, { useState, useEffect } from 'react';

export function StoreFrontSettings({
  storefrontEnabled,
  visibilityType,
  onVisibilityTypeChange,
  onVisibilityToggle,
  getVisibilitySettings,
}) {
  const [isExpanded, setIsExpanded] = useState(storefrontEnabled);

  // Update expansion state when storefrontEnabled changes externally
  useEffect(() => {
    setIsExpanded(storefrontEnabled);
  }, [storefrontEnabled]);

  // Handle toggle for StoreFront section
  const handleToggleStorefront = (e) => {
    e.stopPropagation(); // Prevent event bubbling
    const isEnabled = e.target.checked;
    
    // Toggle StoreFront visibility
    onVisibilityToggle(e);
    
    // Toggle panel open/closed based on the switch state
    setIsExpanded(isEnabled);
  };

  return (
    <div className="settings-input-group-two">
      <div className={`toggle-div-collapsable-1-sub-1 ${isExpanded ? 'expanded' : ''}`}>
        <div className="toggle-text-container-parent">
          <div className="link-primary-icon">
            <img src="/storefront.svg" alt="Storefront" width="16" height="16" />
          </div>
          <div className="toggle-text-container">
            <div className="settings-input-group-h4">StoreFront</div>
            <div className="settings-input-group-h4-sub">
              Enable customizable product page for this asset
            </div>
          </div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <label className="switch">
            <input
              type="checkbox"
              checked={storefrontEnabled}
              onChange={handleToggleStorefront}
            />
            <span className="slider round" />
          </label>
        </div>
      </div>

      {/* Use location-collapse-container for smooth animation */}
      <div className={`location-collapse-container ${isExpanded ? 'expanded' : ''}`}>
        <div
          style={{
            borderTop: '1px solid #E5E7EB',
            margin: '12px 0',
          }}
        />
        <div className="access-level-container">
          <div className="access-level-text">
            <div className="settings-input-group-h4">Access Level</div>
            <div className="settings-input-group-h4-sub">Who can view this product page</div>
          </div>
          <div className="select-wrapper">
            <select
              id="access-level"
              className="input-v2"
              value={visibilityType}
              onChange={(e) => {
                const newVisibility = e.target.value;
                onVisibilityTypeChange(newVisibility, getVisibilitySettings(newVisibility));
              }}
            >
              <option value="private">Private</option>
              <option value="public">Public</option>
              <option value="everyone">Everyone</option>
            </select>
            <img
              src="/expand_down.svg"
              alt=""
              className="select-arrow-icon"
              aria-hidden="true"
            />
          </div>
        </div>
      </div>
    </div>
  );
}