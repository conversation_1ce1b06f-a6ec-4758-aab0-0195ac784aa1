import React, { useState, useEffect } from 'react';
import { Thinkertag } from './Thinkertag';
import { toPng } from 'html-to-image';
import '../styles/Stickers.css';

const initialData = {
  batchId: "f8cf5d87-e7f9-4980-a7b0-383ec0e593c0",
  tags: [
    { url: "https://api.thinkertags.com/Y7H0MH", style: "flagshipII" },
    { url: "https://api.thinkertags.com/M9BLLS", style: "gold" },
    { url: "https://api.thinkertags.com/PAEQGO", style: "instagram" },
    { url: "https://api.thinkertags.com/HMUKLX", style: "whatsapp" }
  ]
};



const Stickers = () => {

  useEffect(() => {
    if (!window.location.hostname.includes('localhost')) {
      return;
    }
  }, []);
  
  const [jsonInput, setJsonInput] = useState(JSON.stringify(initialData, null, 2));
  const [tags, setTags] = useState(initialData.tags);
  const [error, setError] = useState('');
  const [downloading, setDownloading] = useState(false);

  const handleJsonChange = (event) => {
    setJsonInput(event.target.value);
  };

  const handleParse = () => {
    try {
      const parsedData = JSON.parse(jsonInput);
      if (parsedData.tags && Array.isArray(parsedData.tags)) {
        setTags(parsedData.tags);
        setError('');
      } else {
        setError('Invalid JSON structure. Make sure it contains a "tags" array.');
      }
    } catch (err) {
      setError('Invalid JSON. Please check your input.');
    }
  };

  const handleSaveAsPng = async (tagId) => {
    try {
      const element = document.getElementById(`thinkertag-${tagId}`);
      if (element) {
        const scale = 8;
        const dataUrl = await toPng(element, {
          pixelRatio: scale,
          filter: (node) => node.tagName !== 'LINK',
          skipFonts: true, // Skip external font loading
          preferredFontFormat: 'woff2',
          fontEmbedCSS: '', // Empty string to skip font embedding
        });
  
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `${tagId}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Error saving image:', error);
    }
  };

  const handleDownloadAll = async () => {
    setDownloading(true);
    try {
      // Process tags sequentially
      for (const tag of tags) {
        const tagId = tag.url.split('/').pop();
        await handleSaveAsPng(tagId);
        // Add small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      console.log('All stickers downloaded successfully!');
    } catch (error) {
      console.error('Error in batch download:', error);
      setError('Error downloading all stickers. Please try again.');
    } finally {
      setDownloading(false);
    }
  };
  

  return (
    <div className="stickers-container">
      <textarea
        className="json-input"
        value={jsonInput}
        onChange={handleJsonChange}
        placeholder="Paste your JSON here..."
      />
      <div className="button-group">
        <button 
          onClick={handleParse} 
          className="parse-button"
        >
          Parse JSON
        </button>
        <button 
          onClick={handleDownloadAll} 
          className="download-all-button"
          disabled={downloading || tags.length === 0}
        >
          {downloading ? 'Downloading...' : 'Download All'}
        </button>
      </div>
      {error && <p className="error-message">{error}</p>}
      <div className="stickers-grid">
        {tags.map((tag, index) => (
          <div key={index} className="sticker-wrapper">
            <div className="sticker">
              <div id={`thinkertag-${tag.url.split('/').pop()}`}>
                <Thinkertag
                  value={tag.style}
                  tagId={tag.url.split('/').pop()}
                  url={tag.url}
                  disableShadow={true}
                  disableSheen={true} 
                />
              </div>
              <div className="sticker-buttons">
                <button 
                  onClick={() => handleSaveAsPng(tag.url.split('/').pop(), false)} 
                  className="download-button"
                  disabled={downloading}
                >
                  Download
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export { Stickers };