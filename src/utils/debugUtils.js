/**
 * Utility functions for debugging and development
 *
 * Available debug parameters:
 * - debug_skeleton: Forces skeleton loading states in components that support it
 * - debug_pageLoading: Forces the loading state to remain active in the BlogPost component
 * - debug_show_quotes: Shows quote cards on the landing page (hidden by default)
 */

/**
 * Checks if the application is running in development mode
 * @returns {boolean} True if in development mode, false otherwise
 */
export const isDevelopmentMode = () => {
  // Check if we're in development mode based on environment variables
  // Vite sets import.meta.env.DEV to true in development
  return import.meta.env.DEV === true;
};

/**
 * Checks if debug mode is enabled via URL parameters
 * @param {string} debugParam - The debug parameter to check for
 * @returns {boolean} True if debug mode is enabled, false otherwise
 */
export const isDebugModeEnabled = (debugParam) => {
  // Only allow debug mode in development
  if (!isDevelopmentMode()) {
    return false;
  }

  // Check if the debug parameter is in the URL
  return new URLSearchParams(window.location.search).has(debugParam);
};
