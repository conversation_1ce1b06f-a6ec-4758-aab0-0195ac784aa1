import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { Auth, API } from 'aws-amplify';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import ConfirmationDialog from '../components/ConfirmationDialog';
import { BasicDetailsPanel } from '../components/asset/BasicDetailsPanel';
import { StoreFront } from '../components/asset/StoreFront';
import { ConnectedTags } from '../components/asset/ConnectedTags';
import RedirectAuth from '../components/tag/RedirectAuth';
import { MapComponent } from '../components/MapComponent';
import { ToggleSwitch } from '../components/ToggleSwitch';
import { TagActivity } from '../components/TagActivity';
import { AssetPreviewPanel } from '../components/asset/AssetPreviewPanel';
import { ButtonDiv } from '../components/tag/ButtonDiv';

import '../styles/TagList.css';
import '../styles/TagPage.css';
import '../styles/QRPreviewPanel.css';
import '../styles/StatusIndicators.css';

// URL protocol constants for the ensureProtocol function
const PROTOCOLS = [
  'https://', 'http://', 'ws://', 'wss://',
  'ftp://', 'sftp://', 'ftps://',
  'file://', 'smb://', 'nfs://',
  'mailto:', 'tel:', 'sms:', 'facetime:', 'callto:', 'sip:',
  'rtsp://', 'rtmp://', 'spotify:',
  'git://', 'svn://',
  'maps:', 'geo:', 'waze://',
  'market://', 'itms://', 'itms-apps://',
  'steam://', 'discord://', 'slack://', 'teams://', 'zoommtg://', 'meet:', 'skype:', 'whatsapp://', 'telegram://', 'signal://',
  'ldap://', 'jdbc:', 'odbc:',
  'bitcoin:', 'ethereum:',
  'fmp://', 'fmp12://',
];

// Helper function to ensure URL has protocol
function ensureProtocol(url) {
  if (!url) return url;
  const trimmedUrl = url.trim();
  if (!trimmedUrl) return trimmedUrl;

  const hasProtocol = PROTOCOLS.some(protocol =>
    trimmedUrl.toLowerCase().startsWith(protocol)
  );
  if (hasProtocol) {
    return PROTOCOLS.reduce((acc, protocol) => {
      const regex = new RegExp(`${protocol}.*?${protocol}`, 'i');
      return acc.replace(regex, protocol);
    }, trimmedUrl);
  }
  return `https://${trimmedUrl}`;
}

// Reverse geocoding function to get address from coordinates
async function reverseGeocode(latitude, longitude) {
  const apiKey = import.meta.env.VITE_API_KEY_GEOAMPIFY; 
  const url = `https://api.geoapify.com/v1/geocode/reverse?lat=${latitude}&lon=${longitude}&format=json&apiKey=${apiKey}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (!data.results || !data.results.length) {
      return '';
    }
    return data.results[0].formatted;
  } catch (error) {
    console.error('Error during reverse geocoding:', error.message);
    return '';
  }
}

export function AssetPage() {
  const { assetId } = useParams();
  const navigate = useNavigate();

  // Core state
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [activeSegment, setActiveSegment] = useState('Settings');
  const [isOperatorUser, setIsOperatorUser] = useState(false);
  
  // Panel expansion states
  const [isBasicDetailsExpanded, setIsBasicDetailsExpanded] = useState(false);
  
  // Location state variables
  const [isLocationEnabled, setIsLocationEnabled] = useState(false);
  const [coordinates, setCoordinates] = useState({ latitude: null, longitude: null });
  const [address, setAddress] = useState('');
  const [mapRefreshKey, setMapRefreshKey] = useState(0);
  
  // Data state
  const [assetData, setAssetData] = useState({
    id: '',
    name: '',
    description: '',
    address: '',
    publicAction: '',
    privateAction: '',
    tagIds: [],
    serialNumber: '',
    imageUrl: '',
  });
  
  const [formData, setFormData] = useState({
    name: '',
    publicAction: '',
    privateAction: '',
    description: '',
    address: '',
    storefrontEnabled: false,
    publicStorefrontEnabled: false,
    privateStorefrontEnabled: false,
    privateActive: false,
    imageEnabled: true,
  });

  const [serialNumber, setSerialNumber] = useState('');
  
  // Helper functions
  const getVisibilityType = useCallback((publicEnabled, privateEnabled) => {
    if (publicEnabled && privateEnabled) return 'everyone';
    if (publicEnabled) return 'public';
    return 'private';
  }, []);

  const getRedirectValues = useCallback(() => {
    const productPageUrl = `https://thinkertags.com/assets/${assetId}/storefront/view`;
    const redirects = {
      publicAction: formData.publicAction,
      privateAction: formData.privateAction
    };
    
    if (formData.storefrontEnabled) {
      if (formData.publicStorefrontEnabled) {
        redirects.publicAction = productPageUrl;
      }
      
      if (formData.privateStorefrontEnabled) {
        redirects.privateAction = productPageUrl;
      }
    }
    
    return redirects;
  }, [assetId, formData]);

  const getAuthHeaders = useCallback(async () => {
    const session = await Auth.currentSession();
    return {
      Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
      'id-Token': session.getIdToken().getJwtToken(),
    };
  }, []);

  // Check if user is an operator
  useEffect(() => {
    async function checkUserRole() {
      try {
        const user = await Auth.currentAuthenticatedUser();
        const headers = await getAuthHeaders();
        
        const response = await API.get('api', `/users/${encodeURIComponent(user.username)}`, { headers });
        
        if (response && response.role === 'Operator') {
          setIsOperatorUser(true);
        } else {
          setIsOperatorUser(false);
        }
      } catch (err) {
        console.error('Error fetching user role:', err);
      }
    }
    
    checkUserRole();
  }, [getAuthHeaders]);

  // Handler for image upload
  const handleImageUpload = useCallback(async (file) => {
    setIsSaving(true);
    
    try {
      const headers = await getAuthHeaders();
      
      const formData = new FormData();
      formData.append('file', file);
      
      const fileExtension = file.name.split('.').pop().toLowerCase();
      
      const result = await API.post('api', `/assets/${assetId}/upload-image`, {
        headers,
        body: {
          contentType: file.type,
          extension: fileExtension
        }
      });
      
      if (!result || !result.uploadUrl) {
        throw new Error('Failed to get upload URL');
      }
      
      await fetch(result.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      });
      
      await API.post('api', `/assets/${assetId}`, {
        headers,
        body: {
          imageUrl: result.imageUrl
        }
      });
      
      setAssetData(prev => ({
        ...prev,
        imageUrl: result.imageUrl
      }));
      
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [assetId, getAuthHeaders]);

  // Handler for toggling panel expansion
  const handleTogglePanel = useCallback((panel) => {
    if (panel === 'basicDetails') {
      setIsBasicDetailsExpanded(prev => !prev);
    }
  }, []);
  
  // Handler for segment change (Settings/Scans)
  const handleSegmentChange = useCallback((segment) => {
    setActiveSegment(segment);
  }, []);

  // Location handlers
  const handleToggleLocation = useCallback(() => {
    setIsLocationEnabled(prev => !prev);
    setHasUnsavedChanges(true);
  }, []);

  const handleManualUpdateLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setErrorMsg('Geolocation is not supported by this browser.');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitudeVal = position.coords.latitude;
        const longitudeVal = position.coords.longitude;

        setCoordinates({ latitude: latitudeVal, longitude: longitudeVal });
        setHasUnsavedChanges(true);

        try {
          const foundAddress = await reverseGeocode(latitudeVal, longitudeVal);
          setAddress(foundAddress);
          setMapRefreshKey(prev => prev + 1);
        } catch (err) {
          console.error('Could not determine address from latitude/longitude:', err);
          setErrorMsg('Could not determine address from latitude/longitude.');
        }
      },
      (error) => {
        console.error('Unable to retrieve your location:', error);
        setErrorMsg('Unable to retrieve your location. Please try again.');
      }
    );
  }, []);

  // Handle opening map in native app or browser
  const handleOpenMap = useCallback(() => {
    if (coordinates.latitude && coordinates.longitude) {
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      const isAndroid = /Android/.test(navigator.userAgent);
      const isMobile = isIOS || isAndroid || /Mobi|Mobile/i.test(navigator.userAgent);
      
      if (isIOS) {
        window.location.href = `maps:?q=${coordinates.latitude},${coordinates.longitude}`;
      } else if (isAndroid) {
        window.location.href = `geo:${coordinates.latitude},${coordinates.longitude}?q=${coordinates.latitude},${coordinates.longitude}`;
      } else {
        if (!isMobile) {
          window.open(`https://maps.google.com/?q=${coordinates.latitude},${coordinates.longitude}`, '_blank');
        } else {
          window.location.href = `https://maps.google.com/?q=${coordinates.latitude},${coordinates.longitude}`;
        }
      }
    }
  }, [coordinates]);

  // Handler for public URL changes - used by AssetPreviewPanel
  const handlePublicUrlChange = useCallback((e) => {
    if (formData.storefrontEnabled && formData.publicStorefrontEnabled) return;
    
    setFormData(prev => ({
      ...prev,
      publicAction: e.target.value,
    }));
    setHasUnsavedChanges(true);
  }, [formData.storefrontEnabled, formData.publicStorefrontEnabled]);

  // Handler for public URL blur event - used by AssetPreviewPanel
  const handlePublicUrlBlur = useCallback((e) => {
    if (!(formData.storefrontEnabled && formData.publicStorefrontEnabled)) {
      const newValue = ensureProtocol(e.target.value);
      setFormData(prev => ({ ...prev, publicAction: newValue }));
      setHasUnsavedChanges(true);
    }
  }, [formData.storefrontEnabled, formData.publicStorefrontEnabled]);

  // Handlers for RedirectAuth component
  const handlePrivateRedirectToggle = useCallback((isChecked) => {
    if (formData.storefrontEnabled && formData.privateStorefrontEnabled && isChecked === false) {
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      privateActive: isChecked,
      privateAction: isChecked ? prev.privateAction : '',
    }));
    setHasUnsavedChanges(true);
  }, [formData.storefrontEnabled, formData.privateStorefrontEnabled]);

  const handlePrivateUrlChange = useCallback((e) => {
    if (formData.storefrontEnabled && formData.privateStorefrontEnabled) return;
    
    setFormData(prev => ({
      ...prev,
      privateAction: e.target.value,
    }));
    setHasUnsavedChanges(true);
  }, [formData.storefrontEnabled, formData.privateStorefrontEnabled]);

  const handlePrivateUrlBlur = useCallback((e) => {
    if (!(formData.storefrontEnabled && formData.privateStorefrontEnabled)) {
      const newValue = ensureProtocol(e.target.value);
      setFormData(prev => ({ ...prev, privateAction: newValue }));
      setHasUnsavedChanges(true);
    }
  }, [formData.storefrontEnabled, formData.privateStorefrontEnabled]);

  // Effect to update URLs when storefront settings change
  useEffect(() => {
    if (formData.storefrontEnabled) {
      const productPageUrl = `https://thinkertags.com/assets/${assetId}/storefront/view`;
      const newFormData = { ...formData };
      let changed = false;
      
      if (formData.publicStorefrontEnabled && formData.publicAction !== productPageUrl) {
        newFormData.publicAction = productPageUrl;
        changed = true;
      }
      
      if (formData.privateStorefrontEnabled && formData.privateAction !== productPageUrl) {
        newFormData.privateAction = productPageUrl;
        changed = true;
      }
      
      if (changed) {
        setFormData(newFormData);
      }
    }
  }, [assetId, formData.storefrontEnabled, formData.publicStorefrontEnabled, formData.privateStorefrontEnabled]);

  // Fetch asset data on mount
  useEffect(() => {
    let isMounted = true;

    async function fetchAsset() {
      if (!assetId) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const headers = await getAuthHeaders();
        const result = await API.get('api', `/assets/${assetId}`, { headers });
        
        if (!isMounted || !result) return;
        
        const description = result.description || '';
        
        // Set asset data
        setAssetData({
          id: result.id || '',
          name: result.name || '',
          description: result.description2 || '',
          address: description,
          publicAction: result.publicAction || '',
          privateAction: result.privateAction || '',
          tagIds: result.tagIds || [],
          serialNumber: result.customAttribute1 || '',
          imageUrl: result.imageUrl || '',
        });
        
        // Set serialNumber
        setSerialNumber(result.customAttribute1 || '');
        
        // Set visibility settings
        const storefrontEnabled = result.storefrontEnabled || false;
        const publicStorefrontEnabled = result.publicStorefrontEnabled || false;
        const privateStorefrontEnabled = result.privateStorefrontEnabled || false;
        const imageEnabled = result.imageEnabled !== false; // Default to true
        
        // Determine proper redirect URLs based on storefront settings
        let publicAction = result.publicAction || '';
        let privateAction = result.privateAction || '';
        
        // If storefront is enabled, set proper redirects for locked fields with full URL
        if (storefrontEnabled) {
          if (publicStorefrontEnabled) {
            publicAction = `https://thinkertags.com/assets/${result.id}/storefront/view`;
          }
          
          if (privateStorefrontEnabled) {
            privateAction = `https://thinkertags.com/assets/${result.id}/storefront/view`;
          }
        }
        
        // Set location data if available
        if (typeof result.isLocationEnabled === 'boolean') {
          setIsLocationEnabled(result.isLocationEnabled);
          if (result.isLocationEnabled && (result.latitude == null || result.longitude == null)) {
            handleManualUpdateLocation();
          }
          if (result.latitude != null && result.longitude != null) {
            setCoordinates({ latitude: result.latitude, longitude: result.longitude });
            if (result.address) {
              setAddress(result.address.replace(/\n/g, ', '));
            }
          }
        } else if (result.latitude != null && result.longitude != null) {
          setIsLocationEnabled(true);
          setCoordinates({ latitude: result.latitude, longitude: result.longitude });
          if (result.address) {
            setAddress(result.address.replace(/\n/g, ', '));
          }
        }
        
        setFormData({
          name: result.name || '',
          publicAction: publicAction,
          privateAction: privateAction,
          description: result.description2 || '',
          address: description,
          storefrontEnabled,
          publicStorefrontEnabled,
          privateStorefrontEnabled,
          privateActive: !!privateAction, // Set privateActive based on whether there's a privateAction
          imageEnabled,
        });
      } catch (error) {
        console.error('Error fetching asset:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchAsset();
    return () => {
      isMounted = false;
    };
  }, [assetId, getVisibilityType, getAuthHeaders, handleManualUpdateLocation]);

  // Handlers
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setHasUnsavedChanges(true);
  }, []);

  const handleSerialNumberChange = useCallback((value) => {
    setSerialNumber(value);
    setHasUnsavedChanges(true);
  }, []);

  const handleSave = useCallback(async () => {
    if (!hasUnsavedChanges) return;
    
    setIsSaving(true);
    try {
      const headers = await getAuthHeaders();
      const redirectValues = getRedirectValues();
      
      const updateData = {
        // Basic asset info
        name: formData.name,
        description: formData.address,
        description2: formData.description,
        customAttribute1: serialNumber,
        
        // StoreFront settings
        storefrontEnabled: formData.storefrontEnabled,
        publicStorefrontEnabled: formData.publicStorefrontEnabled,
        privateStorefrontEnabled: formData.privateStorefrontEnabled,
        imageEnabled: formData.imageEnabled,
        
        // Update redirects
        publicAction: redirectValues.publicAction,
        privateAction: formData.privateActive ? redirectValues.privateAction : '',
        
        // Location data
        ...(isLocationEnabled && coordinates.latitude && coordinates.longitude && {
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
        }),
        ...(isLocationEnabled && address && { address }),
        isLocationEnabled,
      };

      await API.post('api', `/assets/${assetId}`, { 
        headers, 
        body: updateData 
      });

      // Update local asset data
      setAssetData(prev => ({
        ...prev,
        name: formData.name,
        description: formData.description,
        address: formData.address,
        publicAction: redirectValues.publicAction,
        privateAction: formData.privateActive ? redirectValues.privateAction : '',
        serialNumber: serialNumber,
      }));
      
      // Update formData with correct redirects
      setFormData(prev => ({
        ...prev,
        publicAction: redirectValues.publicAction,
        privateAction: formData.privateActive ? redirectValues.privateAction : ''
      }));
      
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving asset data:', error);
      alert('Failed to save changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [assetId, hasUnsavedChanges, formData, serialNumber, coordinates, address, isLocationEnabled, getAuthHeaders, getRedirectValues]);

  const handleDelete = useCallback(() => {
    setShowDeleteConfirmation(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    setShowDeleteConfirmation(false);
    
    try {
      const headers = await getAuthHeaders();
      await API.del('api', `/assets/${assetId}`, { headers });
      navigate('/assets');
    } catch (error) {
      console.error('Error deleting asset:', error);
      alert('Failed to delete asset. Please try again.');
    }
  }, [assetId, navigate, getAuthHeaders]);

  const handleRemoveTag = useCallback(async (e, tagId) => {
    e.stopPropagation();

    try {
      const headers = await getAuthHeaders();

      const bodyRemoveFromAsset = { tagId, operation: 'remove' };
      await API.post('api', `/assets/${assetId}/updateTagIds`, {
        headers,
        body: bodyRemoveFromAsset,
      });

      const bodyClearAssetOnTag = {
        assetId: '',
        assetName: '',
      };
      await API.post('api', `/tags/${tagId}`, {
        headers,
        body: bodyClearAssetOnTag,
      });

      setAssetData((prev) => ({
        ...prev,
        tagIds: prev.tagIds.filter((tid) => tid !== tagId),
      }));
    } catch (err) {
      console.error('Failed to remove tag from asset or update the tag:', err);
      alert('Failed to remove tag. Please try again.');
    }
  }, [assetId, getAuthHeaders]);

  // Functions for footer button text
  const getFooterButtonText = useCallback(() => {
    if (isSaving) return 'Saving...';
    if (!hasUnsavedChanges) return 'Saved';
    return 'Update';
  }, [isSaving, hasUnsavedChanges]);

  const handleFooterButtonClick = useCallback(() => {
    handleSave();
  }, [handleSave]);

  // Skeleton Helpers
  const renderSkeletonForInputs = () => (
    <>
      <Skeleton height={25} width={140} style={{ marginBottom: '16px' }} />
      <div className="input-group" style={{ marginBottom: '15px' }}>
        <label><Skeleton width={80} /></label>
        <div style={{ paddingTop: '3px' }}>
          <Skeleton height={38} borderRadius={3} />
        </div>
      </div>
      <div className="input-group" style={{ marginBottom: '15px' }}>
        <label><Skeleton width={100} /></label>
        <div style={{ paddingTop: '3px' }}>
          <Skeleton height={38} borderRadius={3} />
        </div>
      </div>
      <div className="input-group" style={{ marginBottom: '15px' }}>
        <label><Skeleton width={90} /></label>
        <div style={{ paddingTop: '3px' }}>
          <Skeleton height={38} borderRadius={3} />
        </div>
      </div>
    </>
  );

  const renderSkeletonForTags = () => (
    <div style={{ marginTop: '30px' }}>
      <Skeleton height={25} width={160} style={{ marginBottom: '10px' }} />
      <div className="tag-list">
        {Array(3).fill(0).map((_, index) => (
          <div className="tag-list-item" key={index}>
            <div className="tag-info">
              <Skeleton width={60} height={14} />
              <Skeleton width={80} height={14} style={{ marginTop: '4px' }} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Main Render
  return (
    <div className="settings-container">
      {/* Header */}
      <div className="settings-header-container">
        <Link to="/assets" className="back-arrow">
          <img src="/arrow_back.svg" alt="Back" className="backarrow" />
          {' '}Assets
        </Link>
      </div>

      {/* Main Panel */}
      <div className="settings-panel">
        {isLoading ? (
          <div className="settings-panel-body" style={{ marginTop: '10px' }}>
            {renderSkeletonForInputs()}
            {renderSkeletonForTags()}
          </div>
        ) : (
          <>
            {/* AssetPreviewPanel */}
            <AssetPreviewPanel
              isLoading={isLoading}
              imageUrl={assetData.imageUrl}
              assetId={assetId}
              assetName={formData.name}
              publicUrl={formData.publicAction}
              isPublicUrlLocked={formData.storefrontEnabled && formData.publicStorefrontEnabled}
              onNameChange={(e) => handleInputChange({...e, name: 'name'})}
              onPublicUrlChange={handlePublicUrlChange}
              onPublicUrlBlur={handlePublicUrlBlur}
              onImageUpload={handleImageUpload}
              isEditing={true}
              imageEnabled={formData.imageEnabled}
              showUploadButton={false}
              useRealImage={formData.storefrontEnabled}
            />

            {/* Basic Details Panel (always shown) */}
            <BasicDetailsPanel
              formData={formData}
              serialNumber={serialNumber}
              isExpanded={isBasicDetailsExpanded}
              onTogglePanel={() => handleTogglePanel('basicDetails')}
              onInputChange={handleInputChange}
              onSerialNumberChange={handleSerialNumberChange}
              isPublicUrlLocked={formData.storefrontEnabled && formData.publicStorefrontEnabled}
              productPageUrl={`https://thinkertags.com/assets/${assetId}/storefront/view`}
            />

            {/* Tabs: Settings | Scans */}
            <ToggleSwitch
              options={['Settings', 'Scans']}
              onChange={handleSegmentChange}
              activeOption={activeSegment}
              specialRounded={['Settings', 'Scans']}
            />

            {/* Settings Tab Content */}
            {activeSegment === 'Settings' && (
              <div className="settings-tab-one-content">
                {/* Settings header - added to match TagPage */}
                <div className="toggle-text-container">
                  <div className="settings-input-group-h3">Settings</div>
                </div>

                {/* Redirect Authenticated (using RedirectAuth component) */}
                <RedirectAuth
                  isLoading={isLoading}
                  isActive={formData.privateActive}
                  isUrlLocked={formData.storefrontEnabled && formData.privateStorefrontEnabled}
                  url={formData.privateAction}
                  productPageUrl={`https://thinkertags.com/assets/${assetId}/storefront/view`}
                  onToggle={handlePrivateRedirectToggle}
                  onUrlChange={handlePrivateUrlChange}
                  onUrlBlur={handlePrivateUrlBlur}
                  ensureProtocol={ensureProtocol}
                />

                {/* Tags in this asset */}
                <ConnectedTags
                  tagIds={assetData.tagIds || []}
                  onRemoveTag={handleRemoveTag}
                  defaultExpanded={false}
                />

                {/* Asset Location */}
                <div className="settings-input-group-two">
                  <div
                    className={`toggle-div-collapsable-1-sub-1 ${
                      isLocationEnabled ? 'expanded' : ''
                    }`}
                  >
                    <div className="toggle-text-container-parent">
                      <div className="link-primary-icon">
                        <img
                          src="/location.svg"
                          alt="Location"
                          width="16"
                          height="16"
                        />
                      </div>
                      <div className="toggle-text-container">
                        <div className="settings-input-group-h4">Asset Location</div>
                        <div className="settings-input-group-h4-sub">
                          Map and manage your asset's location
                        </div>
                      </div>
                    </div>

                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={isLocationEnabled}
                        onChange={handleToggleLocation}
                      />
                      <span className="slider round" />
                    </label>
                  </div>

                  <div
                    className={`location-collapse-container ${
                      isLocationEnabled ? 'expanded' : ''
                    }`}
                  >
                    <div
                      style={{
                        borderTop: '1px solid #E5E7EB',
                        margin: '12px 0',
                      }}
                    />
                    <div className="location-details-row">
                      <div 
                        className="location-info-column"
                        onClick={handleOpenMap}
                        role="button"
                        aria-label="Open location in maps"
                        tabIndex={coordinates.latitude && coordinates.longitude ? 0 : -1}
                      >
                        <div className="address-line">
                          {address || 'No address available'}
                        </div>
                        {coordinates.latitude && coordinates.longitude && (
                          <div className="coords-line">
                            lat: {coordinates.latitude.toFixed(6)} | long: {coordinates.longitude.toFixed(6)}
                          </div>
                        )}
                      </div>
                      <button
                        className="update-location-button"
                        onClick={handleManualUpdateLocation}
                      >
                        Update
                      </button>
                    </div>

                    {coordinates.latitude && coordinates.longitude && (
                      <div
                        style={{
                          borderTop: '1px solid #E5E7EB',
                          margin: '12px 0',
                        }}
                      />
                    )}

                    {/* Map display */}
                    {coordinates.latitude && coordinates.longitude && (
                      <div className="map-container expanded">
                        <MapComponent
                          key={mapRefreshKey}
                          lat={coordinates.latitude}
                          long={coordinates.longitude}
                          mapStyle="standardLight"
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* StoreFront Component - replaced StoreFrontSettings */}
                <StoreFront 
                  assetId={assetId}
                  enabled={formData.storefrontEnabled} 
                />

                {/* More section with Delete Asset */}
                <div>
                  <div className="settings-input-group-h3">More</div>
                  <ButtonDiv
                    data={{
                      title: 'Delete Asset',
                      description: '',
                      iconExpand: 'expand_more',
                      iconCollapse: 'expand_less',
                      subTitle: '',
                      subDescription: isOperatorUser ? 'Admins and Managers can delete Assets' : 'Permanently delete this Asset.',
                      buttonText: isOperatorUser ? 'Delete Asset (Disabled)' : 'Delete Now',
                      isInitiallyExpanded: false,
                    }}
                    onButtonClick={() => {
                      if (!isOperatorUser) {
                        setShowDeleteConfirmation(true);
                      }
                    }}
                    disabled={isOperatorUser}
                  />
                </div>
              </div>
            )}

            {/* Scans Tab Content */}
            {activeSegment === 'Scans' && (
              <div className="settings-tab-two-content">
                <TagActivity assetId={assetId} />
              </div>
            )}

            {/* Footer with Save button - only show on Settings tab */}
            {activeSegment === 'Settings' && (
              <div className="settings-footer">
                <button
                  className={`settings-footer-button ${isSaving ? 'saving' : ''}`}
                  onClick={handleFooterButtonClick}
                  disabled={isSaving || !hasUnsavedChanges}
                >
                  {getFooterButtonText()}
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Custom Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        title="Delete Asset"
        message={`Are you sure you want to delete the asset "${formData.name}"?`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={() => setShowDeleteConfirmation(false)}
        isDanger={true}
      />
    </div>
  );
}