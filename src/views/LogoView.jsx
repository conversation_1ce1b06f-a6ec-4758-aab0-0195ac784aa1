import React, { useEffect, useState, useRef } from 'react';
import '../styles/LogoView.css';

export const LogoView = () => {
  // State for slider values
  const [heightTransform, setHeightTransform] = useState(100); // 100% is normal height
  const [topDeform, setTopDeform] = useState(0); // 0 is no deformation
  const [fontSize, setFontSize] = useState(72); // Default font size in px
  const [letterSpacing, setLetterSpacing] = useState(0); // Letter spacing in px
  const [rotation, setRotation] = useState(0); // Rotation in degrees
  const [skewX, setSkewX] = useState(0); // Skew X in degrees
  const [bounce, setBounce] = useState(0); // Bounce effect intensity
  const [arcCurve, setArcCurve] = useState(0); // Arc curve intensity
  const [showControls, setShowControls] = useState(true);
  const [copySuccess, setCopySuccess] = useState('');
  const [exportStatus, setExportStatus] = useState('');
  const [fontLoaded, setFontLoaded] = useState(false);
  const [opentypeFont, setOpentypeFont] = useState(null);

  // Reference to the logo text element for SVG export
  const logoTextRef = useRef(null);

  // Hide the navbar for this view
  useEffect(() => {
    // Add a class to the body to hide the navbar
    document.body.classList.add('logo-view-page');

    // Load opentype.js
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/opentype.js/1.3.4/opentype.min.js';
    script.onload = () => {
      console.log('opentype.js loaded');
      // Try to load the font
      loadFont();
    };
    document.head.appendChild(script);

    // Clean up when component unmounts
    return () => {
      document.body.classList.remove('logo-view-page');
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  // Load font using opentype.js
  const loadFont = async () => {
    if (typeof window.opentype === 'undefined') {
      console.log('opentype.js not loaded yet');
      return;
    }

    try {
      // Try multiple possible font paths
      const fontPaths = [
        '/fonts/TheBoldFont.ttf',
        '/fonts/TheBoldFont.otf',
        '/fonts/TheBoldFont.woff',
        '/fonts/TheBoldFont.woff2',
        '/assets/fonts/TheBoldFont.ttf',
        '/assets/fonts/TheBoldFont.otf',
        '/public/fonts/TheBoldFont.ttf',
        '/src/assets/fonts/TheBoldFont.ttf'
      ];

      let loadedFont = null;
      
      // Try each path
      for (const path of fontPaths) {
        try {
          console.log(`Trying to load font from: ${path}`);
          loadedFont = await new Promise((resolve, reject) => {
            window.opentype.load(path, (err, font) => {
              if (err) {
                reject(err);
              } else {
                resolve(font);
              }
            });
          });
          
          if (loadedFont) {
            console.log(`Font loaded successfully from: ${path}`);
            setOpentypeFont(loadedFont);
            setFontLoaded(true);
            break;
          }
        } catch (error) {
          console.log(`Failed to load from ${path}:`, error.message);
        }
      }

      if (!loadedFont) {
        console.log('Could not load custom font from any path. Will use fallback for SVG export.');
        // Even if we can't load the custom font, we can still try with a system font
        trySystemFont();
      }
    } catch (error) {
      console.error('Error loading font:', error);
      trySystemFont();
    }
  };

  // Try to load a system font as fallback
  const trySystemFont = async () => {
    try {
      // Try to use Arial Black or another bold system font
      const systemFontUrl = 'https://cdnjs.cloudflare.com/ajax/libs/fonts/0.0.1/Arial-Black.ttf';
      const font = await new Promise((resolve, reject) => {
        window.opentype.load(systemFontUrl, (err, font) => {
          if (err) reject(err);
          else resolve(font);
        });
      });
      
      if (font) {
        setOpentypeFont(font);
        setFontLoaded(true);
      }
    } catch (error) {
      console.log('Could not load system font either. SVG export will use fallback method.');
    }
  };

  // Calculate CSS transform values based on slider positions
  const getTextStyle = () => {
    // Scale Y based on height transform (1 is normal, >1 is taller)
    const scaleY = heightTransform / 100;

    // Build transform string
    let transforms = [];
    
    // Basic transforms
    transforms.push(`scaleY(${scaleY})`);
    
    if (rotation !== 0) {
      transforms.push(`rotate(${rotation}deg)`);
    }
    
    if (skewX !== 0) {
      transforms.push(`skewX(${skewX}deg)`);
    }

    // Perspective transform for top deformation
    if (topDeform > 0) {
      transforms.push(`perspective(500px) rotateX(-${topDeform}deg)`);
    }

    return {
      fontSize: `${fontSize}px`,
      letterSpacing: `${letterSpacing}px`,
      transform: transforms.join(' '),
      transformOrigin: 'center center'
    };
  };

  // Render individual letters with bounce effect
  const renderLettersWithEffects = () => {
    const text = 'THINKERTAGS';
    const letters = text.split('');
    
    return letters.map((letter, index) => {
      let letterTransform = '';
      
      // Apply bounce effect (sine wave)
      if (bounce > 0) {
        const offset = Math.sin((index / letters.length) * Math.PI * 2) * bounce;
        letterTransform = `translateY(${offset}px)`;
      }
      
      // Apply arc curve effect
      if (arcCurve > 0) {
        const totalLetters = letters.length;
        const angle = ((index - totalLetters / 2) / totalLetters) * arcCurve;
        const yOffset = Math.pow(angle, 2) * 0.5;
        letterTransform += ` rotate(${angle}deg) translateY(${yOffset}px)`;
      }
      
      return (
        <span 
          key={index} 
          className="logo-letter"
          style={{ 
            transform: letterTransform,
            display: 'inline-block'
          }}
        >
          {letter}
        </span>
      );
    });
  };

  // Get the CSS transform string for copying
  const getTransformString = () => {
    const style = getTextStyle();
    return `font-size: ${style.fontSize}; letter-spacing: ${style.letterSpacing}; transform: ${style.transform}; transform-origin: ${style.transformOrigin};`;
  };

  // Copy CSS to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(getTransformString())
      .then(() => {
        setCopySuccess('Copied!');
        setTimeout(() => setCopySuccess(''), 2000);
      })
      .catch(err => {
        setCopySuccess('Failed to copy');
        console.error('Could not copy text: ', err);
      });
  };

  // Reset to default values
  const resetValues = () => {
    setHeightTransform(100);
    setTopDeform(0);
    setFontSize(72);
    setLetterSpacing(0);
    setRotation(0);
    setSkewX(0);
    setBounce(0);
    setArcCurve(0);
  };

  // Toggle controls visibility
  const toggleControls = () => {
    setShowControls(!showControls);
  };

  // Export to pure SVG using opentype.js to convert text to paths
  const exportToSVG = () => {
    try {
      setExportStatus('Generating...');
      
      const text = 'THINKERTAGS';
      const svgNS = "http://www.w3.org/2000/svg";
      const svg = document.createElementNS(svgNS, "svg");
      
      // Set SVG dimensions
      const svgWidth = 800;
      const svgHeight = 200;
      svg.setAttribute("width", svgWidth);
      svg.setAttribute("height", svgHeight);
      svg.setAttribute("viewBox", `0 0 ${svgWidth} ${svgHeight}`);
      svg.setAttribute("xmlns", svgNS);
      
      // Add title and description
      const title = document.createElementNS(svgNS, "title");
      title.textContent = "THINKERTAGS Logo";
      svg.appendChild(title);
      
      const desc = document.createElementNS(svgNS, "desc");
      desc.textContent = "Generated logo with transformations applied";
      svg.appendChild(desc);
      
      // Create main group for transformations
      const mainGroup = document.createElementNS(svgNS, "g");
      
      // Apply main transformations
      const transforms = [];
      const scaleY = heightTransform / 100;
      transforms.push(`translate(${svgWidth/2}, ${svgHeight/2})`);
      
      if (rotation !== 0) {
        transforms.push(`rotate(${rotation})`);
      }
      
      if (skewX !== 0) {
        transforms.push(`skewX(${skewX})`);
      }
      
      transforms.push(`scale(1, ${scaleY})`);
      
      mainGroup.setAttribute("transform", transforms.join(" "));
      
      // If we have opentype font loaded, use it to create paths
      if (fontLoaded && opentypeFont && window.opentype) {
        console.log('Using opentype.js to create paths');
        
        // Create paths for each character
        const letters = text.split('');
        let currentX = 0;
        
        // Calculate total width first
        let totalWidth = 0;
        letters.forEach((letter) => {
          const glyph = opentypeFont.charToGlyph(letter);
          const glyphWidth = glyph.advanceWidth * fontSize / opentypeFont.unitsPerEm;
          totalWidth += glyphWidth + letterSpacing;
        });
        totalWidth -= letterSpacing; // Remove last spacing
        
        // Start position (centered)
        currentX = -totalWidth / 2;
        
        // Create path for each letter
        letters.forEach((letter, index) => {
          const letterGroup = document.createElementNS(svgNS, "g");
          
          // Apply individual letter transforms
          let letterTransforms = [];
          let yOffset = 0;
          
          // Apply bounce effect
          if (bounce > 0) {
            yOffset = Math.sin((index / letters.length) * Math.PI * 2) * bounce;
          }
          
          // Apply arc curve effect
          if (arcCurve > 0) {
            const angle = ((index - letters.length / 2) / letters.length) * arcCurve;
            letterTransforms.push(`rotate(${angle})`);
            yOffset += Math.pow(angle, 2) * 0.5;
          }
          
          letterTransforms.push(`translate(${currentX}, ${yOffset})`);
          letterGroup.setAttribute("transform", letterTransforms.join(" "));
          
          // Get the glyph and create path
          const glyph = opentypeFont.charToGlyph(letter);
          const path = glyph.getPath(0, 0, fontSize);
          const pathData = path.toPathData();
          
          const pathElement = document.createElementNS(svgNS, "path");
          pathElement.setAttribute("d", pathData);
          pathElement.setAttribute("fill", "#121212");
          
          letterGroup.appendChild(pathElement);
          mainGroup.appendChild(letterGroup);
          
          // Move to next letter position
          const glyphWidth = glyph.advanceWidth * fontSize / opentypeFont.unitsPerEm;
          currentX += glyphWidth + letterSpacing;
        });
        
      } else {
        // Fallback: use text element
        console.log('Using fallback text element (opentype.js not available or font not loaded)');
        
        const textEl = document.createElementNS(svgNS, "text");
        textEl.setAttribute("x", "0");
        textEl.setAttribute("y", "0");
        textEl.setAttribute("text-anchor", "middle");
        textEl.setAttribute("dominant-baseline", "middle");
        textEl.setAttribute("font-family", "'The Bold Font', Arial Black, sans-serif");
        textEl.setAttribute("font-size", fontSize);
        textEl.setAttribute("font-weight", "bold");
        textEl.setAttribute("fill", "#121212");
        
        if (letterSpacing !== 0) {
          textEl.setAttribute("letter-spacing", letterSpacing);
        }
        
        // Handle individual letter effects
        if (bounce > 0 || arcCurve > 0) {
          const letters = text.split('');
          let currentX = -(letters.length * fontSize * 0.3);
          
          letters.forEach((letter, index) => {
            const tspan = document.createElementNS(svgNS, "tspan");
            tspan.textContent = letter;
            
            let x = currentX;
            let y = 0;
            let rotation = 0;
            
            if (bounce > 0) {
              y = Math.sin((index / letters.length) * Math.PI * 2) * bounce;
            }
            
            if (arcCurve > 0) {
              const angle = ((index - letters.length / 2) / letters.length) * arcCurve;
              rotation = angle;
              y += Math.pow(angle, 2) * 0.5;
            }
            
            tspan.setAttribute("x", x);
            tspan.setAttribute("y", y);
            
            if (rotation !== 0) {
              tspan.setAttribute("transform", `rotate(${rotation} ${x} ${y})`);
            }
            
            textEl.appendChild(tspan);
            currentX += fontSize * 0.6 + letterSpacing;
          });
        } else {
          textEl.textContent = text;
        }
        
        mainGroup.appendChild(textEl);
      }
      
      svg.appendChild(mainGroup);
      
      // Convert SVG to string
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svg);
      
      // Create blob and download
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = 'thinkertags-logo.svg';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      if (fontLoaded && opentypeFont) {
        setExportStatus('Pure SVG exported!');
      } else {
        setExportStatus('SVG exported (text)');
      }
      setTimeout(() => setExportStatus(''), 3000);
    } catch (error) {
      console.error('Error exporting SVG:', error);
      setExportStatus('Error exporting');
      setTimeout(() => setExportStatus(''), 2000);
    }
  };

  const shouldUseLetterEffects = bounce > 0 || arcCurve > 0;

  return (
    <div className="logo-view-container">
      <div className="logo-text" ref={logoTextRef} style={getTextStyle()}>
        {shouldUseLetterEffects ? renderLettersWithEffects() : 'THINKERTAGS'}
      </div>

      {/* Font status indicator */}
      <div className="font-status">
        {fontLoaded ? '✓ Font loaded for path export' : 'Font loading...'}
      </div>

      {/* Toggle button always visible */}
      <button
        className="toggle-controls-button"
        onClick={toggleControls}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {showControls && (
        <div className="logo-controls">
          <div className="controls-grid">
            <div className="control-group">
              <label htmlFor="font-size">Size: {fontSize}px</label>
              <input
                type="range"
                id="font-size"
                min="36"
                max="120"
                value={fontSize}
                onChange={(e) => setFontSize(parseInt(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="height-transform">Height: {heightTransform}%</label>
              <input
                type="range"
                id="height-transform"
                min="50"
                max="200"
                value={heightTransform}
                onChange={(e) => setHeightTransform(parseInt(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="letter-spacing">Spacing: {letterSpacing}px</label>
              <input
                type="range"
                id="letter-spacing"
                min="-5"
                max="20"
                value={letterSpacing}
                onChange={(e) => setLetterSpacing(parseInt(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="rotation">Rotate: {rotation}°</label>
              <input
                type="range"
                id="rotation"
                min="-15"
                max="15"
                step="0.5"
                value={rotation}
                onChange={(e) => setRotation(parseFloat(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="skew-x">Skew: {skewX}°</label>
              <input
                type="range"
                id="skew-x"
                min="-20"
                max="20"
                step="0.5"
                value={skewX}
                onChange={(e) => setSkewX(parseFloat(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="top-deform">Top Deform: {topDeform}</label>
              <input
                type="range"
                id="top-deform"
                min="0"
                max="10"
                step="0.1"
                value={topDeform}
                onChange={(e) => setTopDeform(parseFloat(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="bounce">Bounce: {bounce}px</label>
              <input
                type="range"
                id="bounce"
                min="0"
                max="15"
                step="0.5"
                value={bounce}
                onChange={(e) => setBounce(parseFloat(e.target.value))}
              />
            </div>

            <div className="control-group">
              <label htmlFor="arc-curve">Arc: {arcCurve}°</label>
              <input
                type="range"
                id="arc-curve"
                min="0"
                max="30"
                step="0.5"
                value={arcCurve}
                onChange={(e) => setArcCurve(parseFloat(e.target.value))}
              />
            </div>
          </div>

          <div className="control-buttons">
            <button onClick={resetValues} className="control-button reset-button">
              Reset
            </button>
            <button onClick={copyToClipboard} className="control-button copy-button">
              {copySuccess || 'Copy CSS'}
            </button>
            <button onClick={exportToSVG} className="control-button export-button">
              {exportStatus || 'Export SVG'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LogoView;