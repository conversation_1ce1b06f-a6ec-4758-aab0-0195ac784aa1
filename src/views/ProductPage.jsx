import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Auth, API } from 'aws-amplify';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { ModuleRenderer } from '../components/product/ModuleRenderer';
import FeedbackMessage from '../components/user/FeedbackMessage';
import '../styles/TagPage.css';
import '../styles/QRPreviewPanel.css';

const MESSAGES = {
  SERVICE_REQUEST_SUCCESS: 'Service request sent successfully!',
  SERVICE_REQUEST_FAILURE: 'Failed to send service request. Please try again.',
  NO_RECIPIENT_EMAIL: 'No recipient email configured. Please contact the asset owner.',
  LOAD_ERROR: 'Failed to load asset information.',
  STATUS_UPDATE_ERROR: 'Failed to update status. Please try again.',
  NO_SCAN_DATA: 'No scan data available to download',
  NO_LOG_DATA: 'No log data available to download'
};

const AUTH_TOKEN_HEADER = 'X-Auth-Token';

export function ProductPage() {
  const { assetId } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [assetData, setAssetData] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [modules, setModules] = useState([]);
  const [customerId, setCustomerId] = useState(null);
  const [status, setStatus] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [logs, setLogs] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [scans, setScans] = useState([]);
  const [customStatuses, setCustomStatuses] = useState([]);
  const [isLoadingScans, setIsLoadingScans] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEmailSending, setIsEmailSending] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const scansFetchedRef = useRef(false);
  const feedbackMessageRef = useRef(null);
  const [authToken, setAuthToken] = useState(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  
  const [notesEnabled, setNotesEnabled] = useState(false);
  const [notes, setNotes] = useState('');
  const [documentsEnabled, setDocumentsEnabled] = useState(true);
  
  const [buttonConfig, setButtonConfig] = useState({
    buttonText: 'Request Service',
    buttonEmail: '',
    buttonSubject: '',
    buttonMessage: '',
    buttonEnabled: true,
    showContactForm: true // Default to showing contact form
  });
  
  const safelyUpdateIdToken = async () => {
    try {
      const session = await Auth.currentSession();
      const idToken = session.getIdToken().getJwtToken();
      
      if (idToken) {
        setAuthToken(idToken);
        console.log('Token updated successfully');
      }
      return true;
    } catch (err) {
      console.log('User not authenticated, proceeding as guest');
      console.error('Error updating token:', err);
      return false;
    }
  };
  
  const getAuthHeaders = useCallback(async () => {
    try {
      const session = await Auth.currentSession();
      return {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        'id-Token': session.getIdToken().getJwtToken()
      };
    } catch (error) {
      return {};
    }
  }, []);

  const fetchTagScans = async () => {
    if (!assetId || !isAuthenticated || scansFetchedRef.current) return;
    
    setIsLoadingScans(true);
    try {
      const headers = await getAuthHeaders();
      const response = await API.get('api', `/assets/${assetId}/tag-scans`, { headers });
      
      if (response && Array.isArray(response.scans)) {
        setScans(response.scans);
      } else {
        setScans([]);
      }
      scansFetchedRef.current = true;
    } catch (error) {
      console.error('Error fetching tag scans:', error);
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('User not authorized to view these scans');
      }
    } finally {
      setIsLoadingScans(false);
    }
  };

  const fetchDocumentPreview = useCallback(async (documentId) => {
    if (!documentId) return;
    
    setIsLoadingPreview(true);
    try {
      await safelyUpdateIdToken(); 
      
      let requestHeaders = {};
      
      try {
        const session = await Auth.currentSession();
        const idToken = session.getIdToken().getJwtToken();
        
        requestHeaders[AUTH_TOKEN_HEADER] = idToken;
      } catch (err) {
        console.log('No authenticated session, proceeding as guest');
      }
      
      const response = await API.get('public-api', `/assets/${assetId}/documents/${documentId}`, {
        headers: requestHeaders
      });
      
      if (response) {
        setDocuments(prevDocs => prevDocs.map(doc => {
          if (doc.id === documentId) {
            return {
              ...doc,
              url: response.URL || doc.url,
              type: response.FileName ? response.FileName.split('.').pop().toLowerCase() : (doc.type || 'pdf')
            };
          }
          return doc;
        }));
      }
      
      return response;
    } catch (error) {
      console.error('Error fetching document preview:', error);
    } finally {
      setIsLoadingPreview(false);
    }
  }, [assetId]);

  const handleViewDocument = useCallback(async (documentId, documentName) => {
    try {
      const documentData = await fetchDocumentPreview(documentId);
      
      if (documentData && documentData.URL) {
        window.open(documentData.URL, '_blank');
      } else {
        console.error('No URL available for document:', documentId);
      }
    } catch (error) {
      console.error('Error viewing document:', error);
    }
  }, [fetchDocumentPreview]);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const user = await Auth.currentAuthenticatedUser();
        const session = await Auth.currentSession();
        const idToken = session.getIdToken();
        const tokenPayload = idToken.decodePayload();
        const userCustomerId = tokenPayload['custom:customerId'];
        
        setIsAuthenticated(true);
        setCustomerId(userCustomerId);
        if (assetData && userCustomerId === assetData.customerId) {
          setIsOwner(true);
        }
        
        await safelyUpdateIdToken();
      } catch (error) {
        setIsAuthenticated(false);
        setIsOwner(false);
        console.log('User is not authenticated');
      }
    };
    checkAuthStatus();
  }, [assetData]);

  useEffect(() => {
    const fetchAssetData = async () => {
      if (!assetId) return;
      
      setIsLoading(true);
      setError(null);

      try {
        let response;
        let isAuthenticated = false;

        try {
          const user = await Auth.currentAuthenticatedUser();
          isAuthenticated = true;
          const authHeaders = await getAuthHeaders();
          
          response = await API.get('api', `/assets/${assetId}`, {
            headers: authHeaders
          });
        } catch (authError) {
          console.log('Not authenticated or could not access authenticated endpoint, trying public');
          isAuthenticated = false;
        }

        if (!response) {
          const publicResponse = await fetch(`https://api.thinkertags.com/assets/${assetId}/storefront`);
          if (!publicResponse.ok) {
            throw new Error(`HTTP error! Status: ${publicResponse.status}`);
          }
          response = await publicResponse.json();
        }

        if (response) {
          const normalizedData = {
            ...response,
            address: response.description || '',
            description: response.description2 || '',
            serialNumber: response.customAttribute1 || '',
            imageEnabled: response.imageEnabled !== false,
            aboutUsEnabled: response.aboutUsEnabled !== false,
            notesEnabled: response.notesEnabled || false,
            notes: response.notes || '',
            documentsEnabled: response.documentsEnabled !== false
          };
          
          setNotes(response.notes || '');
          setNotesEnabled(response.notesEnabled || false);
          setDocumentsEnabled(response.documentsEnabled !== false);
          
          if (response.buttonConfig) {
            setButtonConfig(response.buttonConfig);
          } else {
            setButtonConfig({
              buttonText: 'Request Service',
              buttonEmail: '',
              buttonSubject: `Service Request for ${normalizedData.name}`,
              buttonMessage: `A service request has been submitted for ${normalizedData.name}.`,
              buttonEnabled: true,
              showContactForm: true // Default to showing contact form
            });
          }
          
          const defaultStatuses = [
            { id: 'initiated', name: 'Initiated', color: '#f59e0b' }, 
            { id: 'in-progress', name: 'In Progress', color: '#3b82f6' },
            { id: 'on-hold', name: 'On Hold', color: '#6b7280' },
            { id: 'completed', name: 'Completed', color: '#10b981' },
            { id: 'cancelled', name: 'Cancelled', color: '#ef4444' }
          ];
          
          if (response.customStatuses && Array.isArray(response.customStatuses)) {
            normalizedData.customStatuses = response.customStatuses;
            setCustomStatuses(response.customStatuses);
          } else {
            normalizedData.customStatuses = defaultStatuses;
            setCustomStatuses(defaultStatuses);
          }
          
          if (response.status) {
            normalizedData.status = response.status;
            setStatus(response.status);
            setNewStatus(response.status);
          } else {
            normalizedData.status = 'Initiated';
            setStatus('Initiated');
            setNewStatus('Initiated');
          }
          
          if (response.logs && Array.isArray(response.logs)) {
            normalizedData.logs = response.logs;
            setLogs(response.logs);
          } else {
            normalizedData.logs = [];
            setLogs([]);
          }
          
          if (response.documents && Array.isArray(response.documents)) {
            const processedDocuments = response.documents.map(doc => ({
              ...doc,
              type: doc.type || (doc.fileName ? doc.fileName.split('.').pop().toLowerCase() : 'pdf')
            }));
            
            normalizedData.documents = processedDocuments;
            setDocuments(processedDocuments);
            
            if (isAuthenticated) {
              processedDocuments.forEach(doc => {
                if (doc.id && !doc.url) {
                  fetchDocumentPreview(doc.id);
                }
              });
            }
          } else {
            normalizedData.documents = [];
            setDocuments([]);
          }
          
          setAssetData(normalizedData);
          scansFetchedRef.current = false;
        }
      } catch (error) {
        console.error('Error fetching asset data:', error);
        setError(MESSAGES.LOAD_ERROR);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssetData();
  }, [assetId, getAuthHeaders, fetchDocumentPreview]);
  
  useEffect(() => {
    if (assetData && isAuthenticated && !isLoading) {
      fetchTagScans();
    }
  }, [assetData, isAuthenticated, isLoading]);

  useEffect(() => {
    if (!assetData) return;
    
    if (!assetData.storefrontEnabled) {
      setError('This asset does not have a public product page.');
      return;
    }
    
    const hasPublicAccess = !!assetData.publicStorefrontEnabled;
    const hasPrivateAccess = isAuthenticated && 
                           !!assetData.privateStorefrontEnabled && 
                           (isOwner || customerId === assetData.customerId);
    
    if (!hasPublicAccess && !hasPrivateAccess) {
      setError('You do not have access to this product page.');
      return;
    }

    let availableModules = [];
    
    availableModules.push({
      id: 'default-header',
      type: 'header',
      order: 0,
      isPublic: hasPublicAccess,
      isPrivate: hasPrivateAccess,
      config: {}
    });
    
    if (assetData.aboutUsEnabled !== false) {
      availableModules.push({
        id: 'default-aboutUs',
        type: 'aboutUs',
        order: 1,
        isPublic: hasPublicAccess,
        isPrivate: hasPrivateAccess,
        config: {}
      });
    }
    
    if (hasPrivateAccess) {
      availableModules.push({
        id: 'default-statusLogs',
        type: 'statusLogs',
        order: 2,
        isPublic: false,
        isPrivate: true,
        config: {}
      });
      
      if (assetData.notesEnabled === true) {
        availableModules.push({
          id: 'default-notes',
          type: 'notes',
          order: 3,
          isPublic: false,
          isPrivate: true,
          config: {}
        });
      }
    }
    
    if (assetData.documentsEnabled !== false && assetData.documents && assetData.documents.length > 0) {
      availableModules.push({
        id: 'default-documents',
        type: 'documents',
        order: 4,
        isPublic: hasPublicAccess,
        isPrivate: hasPrivateAccess,
        config: {}
      });
    }
    
    if (assetData.storefrontModules && Array.isArray(assetData.storefrontModules)) {
      const customModules = assetData.storefrontModules.filter(module => {
        if (module.isPrivate && hasPrivateAccess) return true;
        if (module.isPublic && hasPublicAccess) return true;
        return false;
      }).sort((a, b) => a.order - b.order);
      
      customModules.forEach(module => {
        if (!availableModules.some(m => m.type === module.type)) {
          availableModules.push(module);
        }
      });
    }
    
    availableModules.sort((a, b) => a.order - b.order);
    
    setModules(availableModules);
  }, [assetData, isAuthenticated, isOwner, customerId]);

  useEffect(() => {
    if (feedbackMessage) {
      if (feedbackMessageRef.current) {
        const rect = feedbackMessageRef.current.getBoundingClientRect();
        const isVisible = (
          rect.top >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
        );
        
        if (!isVisible) {
          feedbackMessageRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
      
      const timer = setTimeout(() => {
        setFeedbackMessage('');
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);
  
  useEffect(() => {
    if (emailSent) {
      const timer = setTimeout(() => {
        setEmailSent(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [emailSent]);
  
  const getStatusOptions = () => {
    if (customStatuses && customStatuses.length > 0) {
      return customStatuses.map(status => status.name);
    }
    
    return [
      'Initiated',
      'In Progress',
      'On Hold',
      'Completed',
      'Cancelled'
    ];
  };

  const handleStatusChange = (newStatus) => {
    if (newStatus === status) return;
    setStatus(newStatus);
    saveStatusChange(newStatus);
  };
  
  const saveStatusChange = async (newStatus) => {
    setIsSaving(true);
    try {
      const headers = await getAuthHeaders();
      const timestamp = new Date().toISOString();
      
      let statusColor = null;
      const statusObj = customStatuses.find(s => s.name === newStatus);
      if (statusObj) {
        statusColor = statusObj.color;
      }
      
      const newLog = {
        status: newStatus,
        timestamp,
        message: `Status changed to ${newStatus}`,
        statusColor
      };
      
      const updatedLogs = [...logs, newLog];
      
      await API.post('api', `/assets/${assetId}`, {
        headers,
        body: {
          status: newStatus,
          logs: updatedLogs
        },
      });
      
      setLogs(updatedLogs);
      setAssetData(prev => ({
        ...prev,
        status: newStatus,
        logs: updatedLogs
      }));
      
    } catch (error) {
      console.error('Error updating status:', error);
      setFeedbackMessage(MESSAGES.STATUS_UPDATE_ERROR);
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDownloadScans = () => {
    if (!scans || scans.length === 0) {
      setFeedbackMessage(MESSAGES.NO_SCAN_DATA);
      return;
    }
    
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Date,Time,Location,Tag ID,Browser,Platform,User Email,Access Type\n";
    
    scans.forEach(scan => {
      const date = new Date(scan.timestamp);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();
      const location = scan.location || 'Unknown';
      const tagId = scan.tagId || 'Unknown';
      const browser = scan.browser || 'Unknown';
      const platform = scan.platform || 'Unknown';
      const userEmail = scan.userEmail || 'Anonymous';
      const accessType = scan.accessType || 'public';
      
      csvContent += `${dateStr},${timeStr},${location},${tagId},${browser},${platform},${userEmail},${accessType}\n`;
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `scans_${assetId}_${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const handleDownloadLogs = () => {
    if (!logs || logs.length === 0) {
      setFeedbackMessage(MESSAGES.NO_LOG_DATA);
      return;
    }
    
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Date,Time,Status,Message\n";
    
    logs.forEach(log => {
      const date = new Date(log.timestamp);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();
      const status = log.status || 'Unknown';
      const message = log.message || '';
      
      csvContent += `${dateStr},${timeStr},${status},"${message}"\n`;
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `logs_${assetId}_${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Updated to handle form data from ServiceButton component and fix CORS issue
  const handleRequestButtonClick = async (formData) => {
    if (!buttonConfig.buttonEmail) {
      setFeedbackMessage(MESSAGES.NO_RECIPIENT_EMAIL);
      return;
    }
    
    setIsEmailSending(true);
    try {
      // Create a request body with the form data and service settings
      const requestBody = {
        userName: formData.userName,
        userEmail: formData.userEmail,
        userMessage: formData.userMessage,
        recipientEmail: buttonConfig.buttonEmail,
        subject: buttonConfig.buttonSubject || `Service Request for ${assetData.name}`,
        message: buttonConfig.buttonMessage || `A service request has been submitted for ${assetData.name}.`,
        productName: assetData.name,
        serialNumber: assetData.serialNumber || ''
      };
      
      // Use Amplify API instead of direct fetch to avoid CORS issues
      const response = await API.post('public-api', `/assets/${assetId}/requestService`, {
        body: requestBody
      });
      
      setFeedbackMessage(MESSAGES.SERVICE_REQUEST_SUCCESS);
      setEmailSent(true);
    } catch (error) {
      console.error('Error sending service request:', error);
      setFeedbackMessage(MESSAGES.SERVICE_REQUEST_FAILURE);
    } finally {
      setIsEmailSending(false);
    }
  };
  
  const renderSkeleton = () => (
    <div className="settings-panel-body">
      <Skeleton height={40} width={300} style={{ marginBottom: '20px' }} />
      <Skeleton height={20} count={2} style={{ marginBottom: '10px' }} />
      <Skeleton height={100} style={{ marginBottom: '20px' }} />
      <Skeleton height={30} width={200} style={{ marginBottom: '10px' }} />
      <Skeleton height={20} count={3} style={{ marginBottom: '5px' }} />
    </div>
  );
  
  const assetDataWithPreview = assetData ? {
    ...assetData,
    buttonText: buttonConfig.buttonText,
    buttonEmail: buttonConfig.buttonEmail,
    buttonSubject: buttonConfig.buttonSubject,
    buttonMessage: buttonConfig.buttonMessage,
    buttonEnabled: buttonConfig.buttonEnabled,
    showContactForm: buttonConfig.showContactForm !== false, // Add showContactForm property
    imageEnabled: assetData.imageEnabled !== false,
    aboutUsEnabled: assetData.aboutUsEnabled !== false,
    status: status,
    logs: logs,
    scans: scans,
    customStatuses: customStatuses,
    notes: notes,
    notesEnabled: notesEnabled,
    documents: documents,
    documentsEnabled: documentsEnabled
  } : null;

  return (
    <div className="settings-container product-page">
      {feedbackMessage && (
        <div ref={feedbackMessageRef}>
          <FeedbackMessage message={feedbackMessage} />
        </div>
      )}

      {isAuthenticated && (
        <div className="settings-header-container">
          <div 
            className="back-arrow" 
            onClick={() => navigate(`/assets/${assetId}/storefront`)}
            role="button"
            tabIndex={0}
            onKeyPress={(e) => e.key === 'Enter' && navigate(`/assets/${assetId}/storefront`)}
          >
            <img
              src="/arrow_back.svg"
              alt="Back"
              className="backarrow"
            />
            {' '}Back to StoreFront
          </div>
        </div>
      )}

      <div className="settings-panel">
        {error ? (
          <div className="settings-panel-body">
            <div className="error-container">
              <FeedbackMessage message={error} />
              {isAuthenticated && !isOwner && (
                <p>You may need additional permissions to access this page.</p>
              )}
              {!isAuthenticated && (
                <p>
                  <a href={`/login?redirect=/assets/${assetId}/storefront/view`} className="link-primary">
                    Sign in
                  </a> to see more information.
                </p>
              )}
            </div>
          </div>
        ) : isLoading ? (
          renderSkeleton()
        ) : (
          <div className="settings-panel-body">
            {!isAuthenticated && assetData?.privateStorefrontEnabled && (
              <div className="note-container">
                <p>
                  You're viewing the public version. 
                  <a href={`/login?redirect=/assets/${assetId}/storefront/view`} className="link-primary">
                    {' '}Sign in
                  </a> to see more information.
                </p>
              </div>
            )}
            
            {isAuthenticated && assetData?.privateStorefrontEnabled && (
              <div className="status-update-section">
                <div className="settings-section">
                  <h3 className="settings-section-title">Status Update</h3>
                  <div className="status-controls">
                    <div className="status-control-row">
                      <div className="status-select-container">
                        <select
                          value={newStatus}
                          onChange={(e) => setNewStatus(e.target.value)}
                          className="input"
                        >
                          {getStatusOptions().map(option => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                      <button
                        className="settings-footer-button"
                        onClick={() => handleStatusChange(newStatus)}
                        disabled={newStatus === status || isSaving}
                      >
                        {isSaving ? 'Updating...' : 'Update Status'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {modules.map(module => (
              <ModuleRenderer 
                key={module.id}
                module={module}
                assetData={assetDataWithPreview}
                isLoading={module.type === 'statusLogs' && isLoadingScans ? isLoadingScans : isLoading}
                isEditing={false}
                getAuthHeaders={getAuthHeaders}
                onStatusChange={handleStatusChange}
                onDownloadScans={handleDownloadScans}
                onDownloadLogs={handleDownloadLogs}
                onRequestButtonClick={handleRequestButtonClick}
                onViewDocument={handleViewDocument}
                isEmailSending={isEmailSending}
              />
            ))}
            
            {emailSent && !feedbackMessage && (
              <FeedbackMessage message={MESSAGES.SERVICE_REQUEST_SUCCESS} />
            )}
            
            {modules.length === 0 && (
              <div className="empty-state">No content available.</div>
            )}
            
            {isOwner && (
              <div className="settings-footer">
                <button
                  className="settings-footer-button"
                  onClick={() => navigate(`/assets/${assetId}/storefront`)}
                >
                  Edit StoreFront
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}